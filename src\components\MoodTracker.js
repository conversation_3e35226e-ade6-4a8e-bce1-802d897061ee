import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import HapticFeedback from 'react-native-haptic-feedback';

const MoodTracker = ({currentMood, onMoodSelect}) => {
  const moods = [
    {key: 'amazing', emoji: '🤩', label: 'Úžasně', color: '#4CAF50'},
    {key: 'good', emoji: '😊', label: 'Dob<PERSON>e', color: '#8BC34A'},
    {key: 'okay', emoji: '😐', label: 'Ujde', color: '#FFC107'},
    {key: 'bad', emoji: '😔', label: 'Špatně', color: '#FF9800'},
    {key: 'terrible', emoji: '😢', label: 'Hrozně', color: '#F44336'},
  ];

  const handleMoodPress = (mood) => {
    HapticFeedback.trigger('impactLight');
    onMoodSelect(mood.key);
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['rgba(255, 255, 255, 0.15)', 'rgba(255, 255, 255, 0.05)']}
        style={styles.card}>
        
        <Text style={styles.title}>Jak se dnes cítíš?</Text>
        <Text style={styles.subtitle}>
          Sledování nálady ti pomůže lépe porozumět svým emocím
        </Text>
        
        <View style={styles.moodGrid}>
          {moods.map((mood) => (
            <TouchableOpacity
              key={mood.key}
              style={[
                styles.moodButton,
                currentMood === mood.key && styles.selectedMood,
                currentMood === mood.key && {borderColor: mood.color},
              ]}
              onPress={() => handleMoodPress(mood)}
              activeOpacity={0.7}>
              
              <Animated.View style={styles.moodContent}>
                <Text style={styles.moodEmoji}>{mood.emoji}</Text>
                <Text style={[
                  styles.moodLabel,
                  currentMood === mood.key && styles.selectedMoodLabel
                ]}>
                  {mood.label}
                </Text>
              </Animated.View>
              
              {currentMood === mood.key && (
                <View style={[styles.selectedIndicator, {backgroundColor: mood.color}]} />
              )}
            </TouchableOpacity>
          ))}
        </View>
        
        {currentMood && (
          <View style={styles.thankYouMessage}>
            <Text style={styles.thankYouText}>
              ✨ Děkujeme za sdílení! Tvoje data pomáhají zlepšovat tvou pohodu.
            </Text>
          </View>
        )}
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  card: {
    borderRadius: 20,
    padding: 24,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    color: '#fff',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 20,
  },
  moodGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  moodButton: {
    flex: 1,
    aspectRatio: 1,
    marginHorizontal: 4,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderWidth: 2,
    borderColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    overflow: 'hidden',
  },
  selectedMood: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    transform: [{scale: 1.05}],
  },
  moodContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  moodEmoji: {
    fontSize: 28,
    marginBottom: 4,
  },
  moodLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
  selectedMoodLabel: {
    color: '#fff',
    fontWeight: '700',
  },
  selectedIndicator: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 3,
    borderRadius: 2,
  },
  thankYouMessage: {
    backgroundColor: 'rgba(76, 175, 80, 0.2)',
    borderRadius: 12,
    padding: 12,
    marginTop: 8,
  },
  thankYouText: {
    color: '#fff',
    fontSize: 13,
    textAlign: 'center',
    fontWeight: '500',
  },
});

export default MoodTracker;
