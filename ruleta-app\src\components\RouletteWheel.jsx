import React from 'react'
import './RouletteWheel.css'

const RouletteWheel = ({ isSpinning, lastResult }) => {
  // Evropská ruleta čísla v pořadí na kole
  const wheelNumbers = [
    0, 32, 15, 19, 4, 21, 2, 25, 17, 34, 6, 27, 13, 36, 11, 30, 8, 23, 10, 5,
    24, 16, 33, 1, 20, 14, 31, 9, 22, 18, 29, 7, 28, 12, 35, 3, 26
  ]

  const getNumberColor = (number) => {
    if (number === 0) return 'green'
    const redNumbers = [1,3,5,7,9,12,14,16,18,19,21,23,25,27,30,32,34,36]
    return redNumbers.includes(number) ? 'red' : 'black'
  }

  const getRotationForNumber = (number) => {
    const index = wheelNumbers.indexOf(number)
    return (index * (360 / 37)) + (Math.random() * 10 - 5) // <PERSON><PERSON><PERSON><PERSON><PERSON> ma<PERSON> náhodnosti
  }

  const wheelRotation = lastResult !== null ? getRotationForNumber(lastResult) : 0

  return (
    <div className="roulette-container">
      <div className="roulette-wheel-wrapper">
        {/* Statický vnější kruh */}
        <div className="wheel-outer-ring"></div>

        {/* Točící se vnitřní část s čísly */}
        <div
          className={`roulette-wheel ${isSpinning ? 'spinning' : ''}`}
          style={{
            transform: `rotate(${wheelRotation + (isSpinning ? 1800 : 0)}deg)`
          }}
        >
          {wheelNumbers.map((number, index) => (
            <div
              key={number}
              className={`wheel-number ${getNumberColor(number)}`}
              style={{
                transform: `rotate(${index * (360 / 37)}deg) translateY(-160px)`
              }}
            >
              {number}
            </div>
          ))}
        </div>

        {/* Statický ukazatel */}
        <div className="wheel-pointer"></div>

        {/* Statický střed */}
        <div className="wheel-center">
          <div className="center-circle">
            <span>RULETA</span>
          </div>
        </div>
      </div>

      {lastResult !== null && !isSpinning && (
        <div className="result-announcement">
          <div className={`result-number ${getNumberColor(lastResult)}`}>
            {lastResult}
          </div>
          <div className="result-text">
            {lastResult === 0 ? 'ZERO!' :
             getNumberColor(lastResult) === 'red' ? 'ČERVENÉ' : 'ČERNÉ'}
          </div>
        </div>
      )}
    </div>
  )
}

export default RouletteWheel
