import PushNotification from 'react-native-push-notification';
import {Platform} from 'react-native';
import StorageService from './StorageService';

class NotificationService {
  constructor() {
    this.initialized = false;
    this.channelId = 'mindflow_notifications';
  }

  async initialize() {
    try {
      // Konfigurace push notifikací
      PushNotification.configure({
        // Callback když je notifikace přijata
        onNotification: (notification) => {
          console.log('Notification received:', notification);
          this.handleNotification(notification);
        },

        // Callback pro registraci tokenu
        onRegister: (token) => {
          console.log('Push token:', token);
          this.savePushToken(token.token);
        },

        // iOS specific
        permissions: {
          alert: true,
          badge: true,
          sound: true,
        },

        // Android specific
        popInitialNotification: true,
        requestPermissions: Platform.OS === 'ios',
      });

      // Vytvoř notification channel pro Android
      if (Platform.OS === 'android') {
        this.createNotificationChannel();
      }

      this.initialized = true;
      console.log('📱 NotificationService initialized');

    } catch (error) {
      console.error('Error initializing NotificationService:', error);
    }
  }

  createNotificationChannel() {
    PushNotification.createChannel(
      {
        channelId: this.channelId,
        channelName: 'MindFlow Notifications',
        channelDescription: 'Wellness reminders and motivation',
        playSound: true,
        soundName: 'default',
        importance: 4,
        vibrate: true,
      },
      (created) => {
        console.log(`Notification channel created: ${created}`);
      }
    );
  }

  async savePushToken(token) {
    try {
      const userData = await StorageService.getUserData();
      userData.pushToken = token;
      await StorageService.saveUserData(userData);
    } catch (error) {
      console.error('Error saving push token:', error);
    }
  }

  handleNotification(notification) {
    // Analytics tracking
    console.log('Notification clicked:', notification.userInfo);
    
    // Deep linking based on notification type
    if (notification.userInfo?.type) {
      this.handleDeepLink(notification.userInfo.type);
    }
  }

  handleDeepLink(type) {
    // Implementace deep linking
    switch (type) {
      case 'mood_reminder':
        // Navigate to mood tracker
        break;
      case 'meditation_reminder':
        // Navigate to meditation
        break;
      case 'streak_celebration':
        // Navigate to progress
        break;
    }
  }

  // Naplánuj denní připomínky
  async scheduleDailyReminders() {
    try {
      const settings = await StorageService.getSettings();
      
      if (!settings.notificationsEnabled) {
        return;
      }

      // Zruš existující připomínky
      this.cancelAllNotifications();

      // Ranní připomínka na mood tracking
      this.scheduleRepeatingNotification({
        id: 'morning_mood',
        title: '🌅 Dobré ráno!',
        message: 'Jak se dnes cítíš? Zaznamenej svou náladu.',
        time: settings.dailyReminderTime || '09:00',
        type: 'mood_reminder',
      });

      // Večerní připomínka na reflexi
      this.scheduleRepeatingNotification({
        id: 'evening_reflection',
        title: '🌙 Čas na reflexi',
        message: 'Jak byl tvůj den? Zkus krátkou meditaci.',
        time: settings.eveningReminderTime || '21:00',
        type: 'meditation_reminder',
      });

      // Týdenní motivace
      this.scheduleWeeklyMotivation();

      console.log('Daily reminders scheduled');

    } catch (error) {
      console.error('Error scheduling daily reminders:', error);
    }
  }

  scheduleRepeatingNotification({id, title, message, time, type}) {
    const [hours, minutes] = time.split(':').map(Number);
    
    PushNotification.localNotificationSchedule({
      id: id,
      channelId: this.channelId,
      title: title,
      message: message,
      date: this.getNextNotificationDate(hours, minutes),
      repeatType: 'day',
      userInfo: {
        type: type,
        id: id,
      },
      playSound: true,
      soundName: 'default',
      vibrate: true,
      vibration: 300,
    });
  }

  getNextNotificationDate(hours, minutes) {
    const now = new Date();
    const notificationTime = new Date();
    notificationTime.setHours(hours, minutes, 0, 0);

    // Pokud je čas už prošlý dnes, naplánuj na zítra
    if (notificationTime <= now) {
      notificationTime.setDate(notificationTime.getDate() + 1);
    }

    return notificationTime;
  }

  scheduleWeeklyMotivation() {
    const motivationalMessages = [
      '🎉 Skvělý týden! Pokračuj v péči o své mentální zdraví.',
      '💪 Jsi na dobré cestě! Každý den se počítá.',
      '🌟 Tvůj pokrok je inspirující! Pokračuj dál.',
      '🚀 Další týden, další příležitost růst!',
      '💝 Pamatuj: péče o sebe není sobectví, je to nutnost.',
    ];

    const randomMessage = motivationalMessages[
      Math.floor(Math.random() * motivationalMessages.length)
    ];

    // Každou neděli v 18:00
    const nextSunday = this.getNextSunday();
    nextSunday.setHours(18, 0, 0, 0);

    PushNotification.localNotificationSchedule({
      id: 'weekly_motivation',
      channelId: this.channelId,
      title: '📊 Týdenní přehled',
      message: randomMessage,
      date: nextSunday,
      repeatType: 'week',
      userInfo: {
        type: 'weekly_stats',
        id: 'weekly_motivation',
      },
      playSound: true,
      soundName: 'default',
    });
  }

  getNextSunday() {
    const now = new Date();
    const daysUntilSunday = 7 - now.getDay();
    const nextSunday = new Date(now);
    nextSunday.setDate(now.getDate() + daysUntilSunday);
    return nextSunday;
  }

  // Okamžité notifikace
  showInstantNotification({title, message, type = 'info'}) {
    PushNotification.localNotification({
      channelId: this.channelId,
      title: title,
      message: message,
      playSound: true,
      soundName: 'default',
      vibrate: true,
      userInfo: {
        type: type,
        timestamp: Date.now(),
      },
    });
  }

  // Streak celebration
  async celebrateStreak(days) {
    const celebrations = {
      7: '🔥 Týden v řadě! Jsi na správné cestě!',
      30: '🎉 Měsíc pokroku! Neuvěřitelné!',
      100: '💎 100 dní! Jsi wellness mistr!',
      365: '👑 Celý rok! Jsi inspirace pro ostatní!',
    };

    if (celebrations[days]) {
      this.showInstantNotification({
        title: `${days} dní v řadě!`,
        message: celebrations[days],
        type: 'streak_celebration',
      });
    }
  }

  // Mood-based notifications
  async sendMoodBasedNotification(mood) {
    const moodNotifications = {
      terrible: {
        title: '💙 Jsme tu pro tebe',
        message: 'Těžké dny jsou součástí života. Zkus dýchací cvičení.',
      },
      bad: {
        title: '🫂 Není to navždy',
        message: 'Každý den je nová příležitost. Zkus krátkou meditaci.',
      },
      okay: {
        title: '🌱 Malé kroky',
        message: 'I malý pokrok je pokrok. Jsi na dobré cestě.',
      },
      good: {
        title: '😊 Skvělé!',
        message: 'Krásný den! Sdílej svou pozitivní energii.',
      },
      amazing: {
        title: '🌟 Úžasné!',
        message: 'Tvoje pozitivní energie je nakažlivá! Pokračuj!',
      },
    };

    const notification = moodNotifications[mood];
    if (notification) {
      // Pošli s malým zpožděním
      setTimeout(() => {
        this.showInstantNotification({
          title: notification.title,
          message: notification.message,
          type: 'mood_response',
        });
      }, 2000);
    }
  }

  // Meditation reminders
  scheduleCustomReminder({title, message, date, type = 'custom'}) {
    PushNotification.localNotificationSchedule({
      channelId: this.channelId,
      title: title,
      message: message,
      date: date,
      playSound: true,
      soundName: 'default',
      userInfo: {
        type: type,
        timestamp: Date.now(),
      },
    });
  }

  // Zruš všechny notifikace
  cancelAllNotifications() {
    PushNotification.cancelAllLocalNotifications();
  }

  // Zruš konkrétní notifikaci
  cancelNotification(id) {
    PushNotification.cancelLocalNotifications({id: id});
  }

  // Získej naplánované notifikace
  getScheduledNotifications() {
    return new Promise((resolve) => {
      PushNotification.getScheduledLocalNotifications((notifications) => {
        resolve(notifications);
      });
    });
  }

  // Aktualizuj nastavení notifikací
  async updateNotificationSettings(settings) {
    try {
      if (settings.notificationsEnabled) {
        await this.scheduleDailyReminders();
      } else {
        this.cancelAllNotifications();
      }
    } catch (error) {
      console.error('Error updating notification settings:', error);
    }
  }

  // Badge management (iOS)
  setBadgeNumber(number) {
    if (Platform.OS === 'ios') {
      PushNotification.setApplicationIconBadgeNumber(number);
    }
  }

  clearBadge() {
    this.setBadgeNumber(0);
  }
}

export default new NotificationService();
