import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Animated,
  Alert,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import HapticFeedback from 'react-native-haptic-feedback';

// Components
import MoodTracker from '../components/MoodTracker';
import AdBanner from '../components/AdBanner';
import QuickActions from '../components/QuickActions';
import WellnessStats from '../components/WellnessStats';
import DailyQuote from '../components/DailyQuote';

// Services
import StorageService from '../services/StorageService';
import AnalyticsService from '../services/AnalyticsService';

const {width, height} = Dimensions.get('window');

const HomeScreen = ({navigation}) => {
  const [userName, setUserName] = useState('');
  const [currentMood, setCurrentMood] = useState(null);
  const [streakDays, setStreakDays] = useState(0);
  const [fadeAnim] = useState(new Animated.Value(0));

  useEffect(() => {
    loadUserData();
    animateEntrance();
    AnalyticsService.trackScreenView('Home');
  }, []);

  const loadUserData = async () => {
    try {
      const userData = await StorageService.getUserData();
      setUserName(userData.name || 'Příteli');
      setStreakDays(userData.streakDays || 0);
      
      const todayMood = await StorageService.getTodayMood();
      setCurrentMood(todayMood);
    } catch (error) {
      console.error('Error loading user data:', error);
    }
  };

  const animateEntrance = () => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start();
  };

  const handleMoodSelect = async (mood) => {
    try {
      HapticFeedback.trigger('impactLight');
      
      await StorageService.saveTodayMood(mood);
      setCurrentMood(mood);
      
      // Update streak
      const newStreak = await StorageService.updateStreak();
      setStreakDays(newStreak);
      
      // Analytics
      AnalyticsService.trackEvent('mood_selected', {mood});
      
      // Show encouraging message
      const moodMessages = {
        amazing: 'Skvělé! Pokračuj v tom, co děláš! ✨',
        good: 'Krásně! Máš dobrý den! 😊',
        okay: 'V pořádku. Možná ti pomůže krátká meditace? 🧘‍♀️',
        bad: 'Chápu. Zkus dýchací cvičení, může to pomoct 🫁',
        terrible: 'Je mi líto. Jsi silnější než si myslíš. Jsme tu pro tebe 💙'
      };
      
      Alert.alert('Děkujeme za sdílení', moodMessages[mood]);
      
    } catch (error) {
      console.error('Error saving mood:', error);
    }
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Dobré ráno';
    if (hour < 18) return 'Dobrý den';
    return 'Dobrý večer';
  };

  const navigateToMeditation = () => {
    HapticFeedback.trigger('impactMedium');
    navigation.navigate('Meditation');
  };

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#667eea', '#764ba2']}
        style={styles.gradient}>
        
        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}>
          
          <Animated.View style={[styles.content, {opacity: fadeAnim}]}>
            
            {/* Header */}
            <View style={styles.header}>
              <View style={styles.headerText}>
                <Text style={styles.greeting}>{getGreeting()},</Text>
                <Text style={styles.userName}>{userName}! 🧠</Text>
              </View>
              
              <TouchableOpacity style={styles.notificationButton}>
                <Icon name="notifications" size={24} color="#fff" />
                <View style={styles.notificationBadge}>
                  <Text style={styles.badgeText}>2</Text>
                </View>
              </TouchableOpacity>
            </View>

            {/* Streak Counter */}
            {streakDays > 0 && (
              <View style={styles.streakContainer}>
                <Icon name="local-fire-department" size={20} color="#ff6b6b" />
                <Text style={styles.streakText}>
                  {streakDays} dní v řadě! Pokračuj! 🔥
                </Text>
              </View>
            )}

            {/* Daily Quote */}
            <DailyQuote />

            {/* Mood Tracker */}
            <MoodTracker
              currentMood={currentMood}
              onMoodSelect={handleMoodSelect}
            />

            {/* Ad Banner */}
            <AdBanner />

            {/* Quick Actions */}
            <QuickActions navigation={navigation} />

            {/* Wellness Stats */}
            <WellnessStats />

            {/* Quick Meditation Button */}
            <TouchableOpacity
              style={styles.quickMeditationButton}
              onPress={navigateToMeditation}
              activeOpacity={0.8}>
              <LinearGradient
                colors={['#ff6b6b', '#ee5a24']}
                style={styles.quickMeditationGradient}>
                <Icon name="play-arrow" size={32} color="#fff" />
                <Text style={styles.quickMeditationText}>
                  Rychlá meditace
                </Text>
                <Text style={styles.quickMeditationSubtext}>
                  3 minuty klidu
                </Text>
              </LinearGradient>
            </TouchableOpacity>

          </Animated.View>
        </ScrollView>
      </LinearGradient>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 100,
  },
  content: {
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    marginTop: 10,
  },
  headerText: {
    flex: 1,
  },
  greeting: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: '400',
  },
  userName: {
    fontSize: 28,
    color: '#fff',
    fontWeight: '700',
    marginTop: 2,
  },
  notificationButton: {
    position: 'relative',
    padding: 8,
  },
  notificationBadge: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: '#ff6b6b',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  streakContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 25,
    marginBottom: 20,
    alignSelf: 'flex-start',
  },
  streakText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  quickMeditationButton: {
    marginTop: 20,
    borderRadius: 20,
    overflow: 'hidden',
    elevation: 8,
    shadowColor: '#ff6b6b',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  quickMeditationGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
    paddingHorizontal: 30,
  },
  quickMeditationText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '700',
    marginLeft: 12,
    flex: 1,
  },
  quickMeditationSubtext: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    fontWeight: '500',
  },
});

export default HomeScreen;
