﻿// MindFlow Test Configuration
export const TEST_CONFIG = {
  // Enable test mode
  TEST_MODE: true,
  
  // Mock data
  USE_MOCK_DATA: true,
  
  // Skip real API calls
  SKIP_ANALYTICS: true,
  SKIP_ADS: false, // Keep ads for testing monetization
  
  // Test user
  TEST_USER: {
    id: 'test_user_demo',
    name: 'Demo UĹľivatel',
    email: '<EMAIL>',
    isPremium: false,
    streakDays: 3,
    totalMeditations: 8
  },
  
  // Test ads (using AdMob test IDs)
  TEST_AD_UNITS: {
    android: {
      banner: 'ca-app-pub-3940256099942544/6300978111',
      interstitial: 'ca-app-pub-3940256099942544/1033173712',
      rewarded: 'ca-app-pub-3940256099942544/5224354917'
    },
    ios: {
      banner: 'ca-app-pub-3940256099942544/2934735716',
      interstitial: 'ca-app-pub-3940256099942544/4411468910', 
      rewarded: 'ca-app-pub-3940256099942544/1712485313'
    }
  },
  
  // Accelerated timers for testing
  FAST_MODE: {
    notificationInterval: 10000, // 10 seconds instead of hours
    adRotationInterval: 5000,    // 5 seconds instead of minutes
    analyticsSync: 30000         // 30 seconds instead of 5 minutes
  }
};
