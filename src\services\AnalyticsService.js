import AsyncStorage from '@react-native-async-storage/async-storage';
import DeviceInfo from 'react-native-device-info';

class AnalyticsService {
  constructor() {
    this.sessionId = null;
    this.userId = null;
    this.deviceInfo = {};
    this.events = [];
    this.initialized = false;
  }

  async initialize() {
    try {
      // Generuj nebo načti user ID
      await this.initializeUserId();
      
      // Získej device info
      await this.collectDeviceInfo();
      
      // Spusť novou session
      this.startSession();
      
      // Na<PERSON>ti uložené eventy
      await this.loadStoredEvents();
      
      // Spusť periodické odesílání
      this.startPeriodicSync();
      
      this.initialized = true;
      console.log('📊 AnalyticsService initialized');
      
      // Track app start
      this.trackEvent('app_started', {
        session_id: this.sessionId,
        device_info: this.deviceInfo,
      });
      
    } catch (error) {
      console.error('Error initializing AnalyticsService:', error);
    }
  }

  async initializeUserId() {
    try {
      let userId = await AsyncStorage.getItem('analytics_user_id');
      
      if (!userId) {
        // Generuj anonymní user ID
        userId = 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        await AsyncStorage.setItem('analytics_user_id', userId);
      }
      
      this.userId = userId;
    } catch (error) {
      console.error('Error initializing user ID:', error);
      this.userId = 'anonymous_' + Date.now();
    }
  }

  async collectDeviceInfo() {
    try {
      this.deviceInfo = {
        device_id: await DeviceInfo.getUniqueId(),
        brand: DeviceInfo.getBrand(),
        model: DeviceInfo.getModel(),
        system_name: DeviceInfo.getSystemName(),
        system_version: DeviceInfo.getSystemVersion(),
        app_version: DeviceInfo.getVersion(),
        build_number: DeviceInfo.getBuildNumber(),
        bundle_id: DeviceInfo.getBundleId(),
        is_tablet: DeviceInfo.isTablet(),
        has_notch: await DeviceInfo.hasNotch(),
        timezone: DeviceInfo.getTimezone(),
        locale: await DeviceInfo.getLocales(),
        total_memory: await DeviceInfo.getTotalMemory(),
        used_memory: await DeviceInfo.getUsedMemory(),
        battery_level: await DeviceInfo.getBatteryLevel(),
        is_emulator: await DeviceInfo.isEmulator(),
      };
    } catch (error) {
      console.error('Error collecting device info:', error);
      this.deviceInfo = {
        error: 'Failed to collect device info',
      };
    }
  }

  startSession() {
    this.sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    console.log('📊 New analytics session:', this.sessionId);
  }

  async loadStoredEvents() {
    try {
      const storedEvents = await AsyncStorage.getItem('analytics_events');
      if (storedEvents) {
        this.events = JSON.parse(storedEvents);
        console.log(`📊 Loaded ${this.events.length} stored events`);
      }
    } catch (error) {
      console.error('Error loading stored events:', error);
      this.events = [];
    }
  }

  async saveEvents() {
    try {
      await AsyncStorage.setItem('analytics_events', JSON.stringify(this.events));
    } catch (error) {
      console.error('Error saving events:', error);
    }
  }

  startPeriodicSync() {
    // Odesílej data každých 5 minut
    setInterval(() => {
      this.syncEvents();
    }, 5 * 60 * 1000);

    // Odesílej při ukončení aplikace
    // AppState.addEventListener('change', (nextAppState) => {
    //   if (nextAppState === 'background') {
    //     this.syncEvents();
    //   }
    // });
  }

  // Hlavní metoda pro tracking eventů
  async trackEvent(eventName, properties = {}) {
    try {
      const event = {
        event_name: eventName,
        user_id: this.userId,
        session_id: this.sessionId,
        timestamp: Date.now(),
        properties: {
          ...properties,
          platform: this.deviceInfo.system_name,
          app_version: this.deviceInfo.app_version,
        },
      };

      this.events.push(event);
      
      // Uložení pro offline podporu
      await this.saveEvents();
      
      console.log('📊 Event tracked:', eventName, properties);
      
      // Pokud je příliš mnoho eventů, odesli je
      if (this.events.length >= 50) {
        this.syncEvents();
      }
      
    } catch (error) {
      console.error('Error tracking event:', error);
    }
  }

  // Screen view tracking
  async trackScreenView(screenName, properties = {}) {
    await this.trackEvent('screen_view', {
      screen_name: screenName,
      ...properties,
    });
  }

  // User properties
  async setUserProperties(properties) {
    await this.trackEvent('user_properties_updated', {
      user_properties: properties,
    });
  }

  // Mood tracking analytics
  async trackMoodEntry(mood, previousMood = null) {
    await this.trackEvent('mood_entry', {
      mood: mood,
      previous_mood: previousMood,
      time_of_day: new Date().getHours(),
      day_of_week: new Date().getDay(),
    });
  }

  // Meditation analytics
  async trackMeditationStarted(meditationType, duration) {
    await this.trackEvent('meditation_started', {
      meditation_type: meditationType,
      planned_duration: duration,
      time_of_day: new Date().getHours(),
    });
  }

  async trackMeditationCompleted(meditationType, actualDuration, plannedDuration) {
    await this.trackEvent('meditation_completed', {
      meditation_type: meditationType,
      actual_duration: actualDuration,
      planned_duration: plannedDuration,
      completion_rate: actualDuration / plannedDuration,
    });
  }

  // Ad analytics
  async trackAdImpression(adId, adType, placement) {
    await this.trackEvent('ad_impression', {
      ad_id: adId,
      ad_type: adType,
      placement: placement,
    });
  }

  async trackAdClick(adId, adType, placement) {
    await this.trackEvent('ad_click', {
      ad_id: adId,
      ad_type: adType,
      placement: placement,
    });
  }

  // Premium analytics
  async trackPremiumUpgrade(plan, price) {
    await this.trackEvent('premium_upgrade', {
      plan: plan,
      price: price,
      user_age_days: await this.getUserAgeDays(),
    });
  }

  async trackPremiumCancellation(reason) {
    await this.trackEvent('premium_cancellation', {
      reason: reason,
      user_age_days: await this.getUserAgeDays(),
    });
  }

  // Engagement analytics
  async trackSessionDuration(duration) {
    await this.trackEvent('session_ended', {
      session_duration: duration,
      events_in_session: this.getSessionEventCount(),
    });
  }

  async trackFeatureUsage(featureName, usageData = {}) {
    await this.trackEvent('feature_used', {
      feature_name: featureName,
      ...usageData,
    });
  }

  // Retention analytics
  async trackDailyActive() {
    const today = new Date().toDateString();
    const lastActive = await AsyncStorage.getItem('last_active_date');
    
    if (lastActive !== today) {
      await this.trackEvent('daily_active_user', {
        date: today,
        days_since_install: await this.getDaysSinceInstall(),
      });
      
      await AsyncStorage.setItem('last_active_date', today);
    }
  }

  // Error tracking
  async trackError(error, context = {}) {
    await this.trackEvent('error_occurred', {
      error_message: error.message,
      error_stack: error.stack,
      context: context,
    });
  }

  // Performance tracking
  async trackPerformance(metricName, value, unit = 'ms') {
    await this.trackEvent('performance_metric', {
      metric_name: metricName,
      value: value,
      unit: unit,
    });
  }

  // Funnel tracking
  async trackFunnelStep(funnelName, stepName, stepIndex) {
    await this.trackEvent('funnel_step', {
      funnel_name: funnelName,
      step_name: stepName,
      step_index: stepIndex,
    });
  }

  // A/B Testing
  async trackExperiment(experimentName, variant) {
    await this.trackEvent('experiment_exposure', {
      experiment_name: experimentName,
      variant: variant,
    });
  }

  // Helper methods
  async getUserAgeDays() {
    try {
      const installDate = await AsyncStorage.getItem('app_install_date');
      if (installDate) {
        const install = new Date(installDate);
        const now = new Date();
        return Math.floor((now - install) / (1000 * 60 * 60 * 24));
      }
      return 0;
    } catch (error) {
      return 0;
    }
  }

  async getDaysSinceInstall() {
    return await this.getUserAgeDays();
  }

  getSessionEventCount() {
    return this.events.filter(event => event.session_id === this.sessionId).length;
  }

  // Data synchronization
  async syncEvents() {
    if (this.events.length === 0) {
      return;
    }

    try {
      console.log(`📊 Syncing ${this.events.length} events...`);
      
      // Simulace odesílání na server
      const success = await this.sendEventsToServer(this.events);
      
      if (success) {
        // Vymaž odeslaná data
        this.events = [];
        await AsyncStorage.removeItem('analytics_events');
        console.log('📊 Events synced successfully');
      } else {
        console.log('📊 Sync failed, keeping events for retry');
      }
      
    } catch (error) {
      console.error('Error syncing events:', error);
    }
  }

  async sendEventsToServer(events) {
    try {
      // Simulace API call
      // V reálné aplikaci by zde byl fetch() na analytics endpoint
      
      // Pro demo účely simulujeme úspěšné odeslání
      console.log('📊 Simulating server sync for events:', events.length);
      
      // Simulace network delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Simulace úspěchu (90% úspěšnost)
      return Math.random() > 0.1;
      
    } catch (error) {
      console.error('Error sending events to server:', error);
      return false;
    }
  }

  // Privacy compliance
  async optOut() {
    try {
      this.events = [];
      await AsyncStorage.removeItem('analytics_events');
      await AsyncStorage.setItem('analytics_opt_out', 'true');
      console.log('📊 User opted out of analytics');
    } catch (error) {
      console.error('Error opting out:', error);
    }
  }

  async optIn() {
    try {
      await AsyncStorage.removeItem('analytics_opt_out');
      console.log('📊 User opted in to analytics');
    } catch (error) {
      console.error('Error opting in:', error);
    }
  }

  async isOptedOut() {
    try {
      const optOut = await AsyncStorage.getItem('analytics_opt_out');
      return optOut === 'true';
    } catch (error) {
      return false;
    }
  }

  // Data export for GDPR
  async exportUserData() {
    try {
      const userData = {
        user_id: this.userId,
        device_info: this.deviceInfo,
        events: this.events,
        export_date: new Date().toISOString(),
      };
      
      return JSON.stringify(userData, null, 2);
    } catch (error) {
      console.error('Error exporting user data:', error);
      return null;
    }
  }

  // Clear all data
  async clearAllData() {
    try {
      this.events = [];
      await AsyncStorage.multiRemove([
        'analytics_user_id',
        'analytics_events',
        'last_active_date',
        'app_install_date',
      ]);
      console.log('📊 All analytics data cleared');
    } catch (error) {
      console.error('Error clearing analytics data:', error);
    }
  }

  // Get analytics summary
  getAnalyticsSummary() {
    return {
      user_id: this.userId,
      session_id: this.sessionId,
      events_count: this.events.length,
      device_info: this.deviceInfo,
      initialized: this.initialized,
    };
  }
}

export default new AnalyticsService();
