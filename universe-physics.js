// Universe Physics Engine - Brutální fyzikální simulace
class UniversePhysics {
    constructor() {
        // Fyzikální konstanty
        this.G = 6.67430e-11;  // Gravitační konstanta
        this.c = 299792458;    // Rychlost světla
        this.h = 6.62607015e-34; // Planckova konstanta
        this.k = 1.380649e-23; // Boltzmannova konstanta

        // Simulační parametry
        this.timeScale = 1.0;
        this.gravityMultiplier = 1.0;
        this.enableRelativity = false;
        this.enableCollisions = true;
        this.enableTrails = true;

        // Objekty ve vesmíru
        this.objects = [];
        this.trails = [];
        this.explosions = [];

        // Statistiky
        this.totalMass = 0;
        this.totalEnergy = 0;
        this.simulationTime = 0;

        // Výpočetní optimalizace
        this.spatialGrid = new Map();
        this.gridSize = 1e10; // 10 miliard metrů na buňku
    }

    // Vytvoření nového objektu
    createObject(type, x, y, mass, vx = 0, vy = 0) {
        const obj = {
            id: Date.now() + Math.random(),
            type: type,
            x: x,
            y: y,
            vx: vx,
            vy: vy,
            mass: mass,
            radius: this.calculateRadius(mass, type),
            color: this.getObjectColor(type),
            temperature: this.calculateTemperature(mass, type),
            luminosity: this.calculateLuminosity(mass, type),
            age: 0,
            trail: [],
            isDestroyed: false,

            // Pokročilé vlastnosti
            angularVelocity: 0,
            magneticField: this.calculateMagneticField(mass, type),
            composition: this.getComposition(type),
            atmosphere: this.getAtmosphere(type),

            // Relativistické efekty
            properTime: 0,
            gravitationalRedshift: 0,

            // Kvantové vlastnosti (pro malé objekty)
            waveFunction: type === 'particle' ? this.initializeWaveFunction() : null
        };

        this.objects.push(obj);
        this.updateSpatialGrid(obj);
        return obj;
    }

    // Výpočet poloměru podle hmotnosti a typu
    calculateRadius(mass, type) {
        switch(type) {
            case 'star':
                // Hvězdy: R ∝ M^0.8
                return Math.pow(mass / 1.989e30, 0.8) * 6.96e8;
            case 'planet':
                // Planety: R ∝ M^0.27
                return Math.pow(mass / 5.972e24, 0.27) * 6.371e6;
            case 'blackhole':
                // Schwarzschildův poloměr: Rs = 2GM/c²
                return (2 * this.G * mass) / (this.c * this.c);
            case 'asteroid':
                // Asteroidy: hustota ~2000 kg/m³
                return Math.pow((3 * mass) / (4 * Math.PI * 2000), 1/3);
            case 'ship':
                return 100; // 100 metrů
            default:
                return Math.pow(mass / 1000, 1/3);
        }
    }

    // Barva objektu podle typu
    getObjectColor(type) {
        const colors = {
            star: '#ffff00',
            planet: '#4a90e2',
            blackhole: '#000000',
            asteroid: '#8b4513',
            ship: '#00ff00',
            particle: '#ff00ff'
        };
        return colors[type] || '#ffffff';
    }

    // Výpočet teploty
    calculateTemperature(mass, type) {
        switch(type) {
            case 'star':
                // Teplota hvězdy podle hmotnosti
                return 5778 * Math.pow(mass / 1.989e30, 0.5);
            case 'planet':
                // Základní teplota planety
                return 288; // Kelvin
            case 'blackhole':
                // Hawkingova teplota: T = ħc³/(8πGMk)
                return (this.h * Math.pow(this.c, 3)) / (8 * Math.PI * this.G * mass * this.k);
            default:
                return 2.7; // Kosmické mikrovlnné pozadí
        }
    }

    // Výpočet svítivosti
    calculateLuminosity(mass, type) {
        if (type === 'star') {
            // Stefan-Boltzmannův zákon: L ∝ M^3.5
            return 3.828e26 * Math.pow(mass / 1.989e30, 3.5);
        }
        return 0;
    }

    // Magnetické pole
    calculateMagneticField(mass, type) {
        switch(type) {
            case 'star':
                return 1e-4 * Math.sqrt(mass / 1.989e30); // Tesla
            case 'planet':
                return 3e-5 * Math.sqrt(mass / 5.972e24);
            default:
                return 0;
        }
    }

    // Složení objektu
    getComposition(type) {
        const compositions = {
            star: { hydrogen: 73, helium: 25, metals: 2 },
            planet: { rock: 70, iron: 20, water: 10 },
            asteroid: { rock: 80, iron: 15, ice: 5 },
            blackhole: { singularity: 100 }
        };
        return compositions[type] || { unknown: 100 };
    }

    // Atmosféra
    getAtmosphere(type) {
        const atmospheres = {
            planet: { nitrogen: 78, oxygen: 21, argon: 1 },
            star: { plasma: 100 }
        };
        return atmospheres[type] || null;
    }

    // Kvantová vlnová funkce
    initializeWaveFunction() {
        return {
            amplitude: 1.0,
            phase: 0,
            uncertainty: this.h / (4 * Math.PI)
        };
    }

    // Hlavní fyzikální update
    update(deltaTime) {
        const dt = deltaTime * this.timeScale;
        this.simulationTime += dt;

        // Vyčištění prostorové mřížky
        this.spatialGrid.clear();

        // Aktualizace prostorové mřížky
        this.objects.forEach(obj => this.updateSpatialGrid(obj));

        // Gravitační síly
        this.calculateGravitationalForces(dt);

        // Relativistické efekty
        if (this.enableRelativity) {
            this.calculateRelativisticEffects(dt);
        }

        // Pohyb objektů
        this.updatePositions(dt);

        // Kolize
        if (this.enableCollisions) {
            this.checkCollisions();
        }

        // Stopy
        if (this.enableTrails) {
            this.updateTrails();
        }

        // Evoluce objektů
        this.evolveObjects(dt);

        // Statistiky
        this.updateStatistics();

        // Vyčištění zničených objektů
        this.cleanupDestroyed();
    }

    // Prostorová mřížka pro optimalizaci
    updateSpatialGrid(obj) {
        const gridX = Math.floor(obj.x / this.gridSize);
        const gridY = Math.floor(obj.y / this.gridSize);
        const key = `${gridX},${gridY}`;

        if (!this.spatialGrid.has(key)) {
            this.spatialGrid.set(key, []);
        }
        this.spatialGrid.get(key).push(obj);
    }

    // Výpočet gravitačních sil
    calculateGravitationalForces(dt) {
        this.objects.forEach(obj1 => {
            if (obj1.isDestroyed) return;

            obj1.fx = 0;
            obj1.fy = 0;

            // Najdi objekty v okolních buňkách
            const nearbyObjects = this.getNearbyObjects(obj1);

            nearbyObjects.forEach(obj2 => {
                if (obj1.id === obj2.id || obj2.isDestroyed) return;

                const dx = obj2.x - obj1.x;
                const dy = obj2.y - obj1.y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance < obj1.radius + obj2.radius) {
                    // Kolize bude řešena později
                    return;
                }

                // Newtonův gravitační zákon: F = G * m1 * m2 / r²
                const force = this.G * obj1.mass * obj2.mass / (distance * distance) * this.gravityMultiplier;

                // Relativistická korekce pro silná gravitační pole
                let correction = 1.0;
                if (this.enableRelativity) {
                    const rs1 = (2 * this.G * obj1.mass) / (this.c * this.c);
                    const rs2 = (2 * this.G * obj2.mass) / (this.c * this.c);
                    correction = 1 + 1.5 * (rs1 + rs2) / distance;
                }

                const fx = force * dx / distance * correction;
                const fy = force * dy / distance * correction;

                obj1.fx += fx;
                obj1.fy += fy;
            });
        });
    }

    // Najdi objekty v okolí
    getNearbyObjects(obj) {
        const gridX = Math.floor(obj.x / this.gridSize);
        const gridY = Math.floor(obj.y / this.gridSize);
        const nearby = [];

        // Prohledej 3x3 oblast kolem objektu
        for (let dx = -1; dx <= 1; dx++) {
            for (let dy = -1; dy <= 1; dy++) {
                const key = `${gridX + dx},${gridY + dy}`;
                if (this.spatialGrid.has(key)) {
                    nearby.push(...this.spatialGrid.get(key));
                }
            }
        }

        return nearby;
    }

    // Relativistické efekty
    calculateRelativisticEffects(dt) {
        this.objects.forEach(obj => {
            if (obj.isDestroyed) return;

            // Rychlost objektu
            const v = Math.sqrt(obj.vx * obj.vx + obj.vy * obj.vy);

            // Lorentzův faktor: γ = 1/√(1 - v²/c²)
            const gamma = 1 / Math.sqrt(1 - (v * v) / (this.c * this.c));

            // Dilatace času
            obj.properTime += dt / gamma;

            // Gravitační červený posuv
            const nearbyMass = this.getNearbyMass(obj);
            const gravitationalPotential = this.G * nearbyMass / Math.max(obj.radius * 10, 1e6);
            obj.gravitationalRedshift = gravitationalPotential / (this.c * this.c);
        });
    }

    // Celková hmotnost v okolí
    getNearbyMass(obj) {
        let totalMass = 0;
        const searchRadius = obj.radius * 100;

        this.objects.forEach(other => {
            if (other.id === obj.id || other.isDestroyed) return;

            const dx = other.x - obj.x;
            const dy = other.y - obj.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance < searchRadius) {
                totalMass += other.mass;
            }
        });

        return totalMass;
    }

    // Aktualizace pozic
    updatePositions(dt) {
        this.objects.forEach(obj => {
            if (obj.isDestroyed) return;

            // Zrychlení: a = F/m
            const ax = obj.fx / obj.mass;
            const ay = obj.fy / obj.mass;

            // Verlet integrace pro lepší stabilitu
            const newX = obj.x + obj.vx * dt + 0.5 * ax * dt * dt;
            const newY = obj.y + obj.vy * dt + 0.5 * ay * dt * dt;

            obj.vx += ax * dt;
            obj.vy += ay * dt;

            obj.x = newX;
            obj.y = newY;

            // Rotace
            obj.angularVelocity += (Math.random() - 0.5) * 0.001;

            // Stárnutí
            obj.age += dt;
        });
    }

    // Kontrola kolizí
    checkCollisions() {
        for (let i = 0; i < this.objects.length; i++) {
            for (let j = i + 1; j < this.objects.length; j++) {
                const obj1 = this.objects[i];
                const obj2 = this.objects[j];

                if (obj1.isDestroyed || obj2.isDestroyed) continue;

                const dx = obj2.x - obj1.x;
                const dy = obj2.y - obj1.y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance < obj1.radius + obj2.radius) {
                    this.handleCollision(obj1, obj2);
                }
            }
        }
    }

    // Řešení kolize
    handleCollision(obj1, obj2) {
        // Určí, který objekt "vyhraje"
        const winner = obj1.mass > obj2.mass ? obj1 : obj2;
        const loser = obj1.mass > obj2.mass ? obj2 : obj1;

        // Zachování hybnosti
        const totalMass = winner.mass + loser.mass;
        const newVx = (winner.mass * winner.vx + loser.mass * loser.vx) / totalMass;
        const newVy = (winner.mass * winner.vy + loser.mass * loser.vy) / totalMass;

        // Aktualizace vítěze
        winner.mass = totalMass;
        winner.vx = newVx;
        winner.vy = newVy;
        winner.radius = this.calculateRadius(winner.mass, winner.type);
        winner.temperature = this.calculateTemperature(winner.mass, winner.type);

        // Zničení poraženého
        loser.isDestroyed = true;

        // Vytvoření exploze
        this.createExplosion(loser.x, loser.y, loser.mass);

        console.log(`Kolize: ${winner.type} (${winner.mass.toExponential(2)} kg) pohltil ${loser.type}`);
    }

    // Vytvoření exploze
    createExplosion(x, y, mass) {
        const explosion = {
            x: x,
            y: y,
            energy: mass * this.c * this.c * 0.001, // E=mc² (0.1% konverze)
            time: 0,
            maxTime: 2.0,
            particles: []
        };

        // Vytvoření částic exploze
        const particleCount = Math.min(50, Math.sqrt(mass / 1e20));
        for (let i = 0; i < particleCount; i++) {
            const angle = (i / particleCount) * 2 * Math.PI;
            const speed = Math.sqrt(explosion.energy / mass) * 0.1;

            explosion.particles.push({
                x: x,
                y: y,
                vx: Math.cos(angle) * speed,
                vy: Math.sin(angle) * speed,
                life: 1.0
            });
        }

        this.explosions.push(explosion);
    }

    // Aktualizace stop
    updateTrails() {
        this.objects.forEach(obj => {
            if (obj.isDestroyed) return;

            // Přidej nový bod do stopy
            obj.trail.push({ x: obj.x, y: obj.y, time: this.simulationTime });

            // Omeз délku stopy
            const maxTrailLength = 100;
            if (obj.trail.length > maxTrailLength) {
                obj.trail.shift();
            }

            // Odstraň staré body
            const maxAge = 10; // sekund
            obj.trail = obj.trail.filter(point =>
                this.simulationTime - point.time < maxAge
            );
        });
    }

    // Evoluce objektů v čase
    evolveObjects(dt) {
        this.objects.forEach(obj => {
            if (obj.isDestroyed) return;

            switch(obj.type) {
                case 'star':
                    this.evolveStar(obj, dt);
                    break;
                case 'planet':
                    this.evolvePlanet(obj, dt);
                    break;
                case 'blackhole':
                    this.evolveBlackHole(obj, dt);
                    break;
            }
        });

        // Aktualizace explozí
        this.explosions = this.explosions.filter(explosion => {
            explosion.time += dt;

            // Aktualizace částic
            explosion.particles.forEach(particle => {
                particle.x += particle.vx * dt;
                particle.y += particle.vy * dt;
                particle.life -= dt / explosion.maxTime;
            });

            return explosion.time < explosion.maxTime;
        });
    }

    // Evoluce hvězdy
    evolveStar(star, dt) {
        // Jaderná fúze - přeměna hmotnosti na energii
        const fusionRate = star.mass * 1e-18; // kg/s
        star.mass -= fusionRate * dt;

        // Změna teploty a svítivosti
        star.temperature = this.calculateTemperature(star.mass, 'star');
        star.luminosity = this.calculateLuminosity(star.mass, 'star');

        // Hvězdný vítr
        const windMass = star.luminosity / (this.c * this.c) * dt;
        star.mass -= windMass;

        // Kontrola konce života hvězdy
        if (star.mass < 0.08 * 1.989e30) { // Pod hranicí hnědého trpaslíka
            this.stellarDeath(star);
        }
    }

    // Smrt hvězdy
    stellarDeath(star) {
        const originalMass = star.mass;

        if (originalMass > 25 * 1.989e30) {
            // Supernova -> černá díra
            star.type = 'blackhole';
            star.mass = originalMass * 0.3; // Zbytek se rozletí
            star.radius = this.calculateRadius(star.mass, 'blackhole');
            star.color = '#000000';

            // Vytvoř supernovu
            this.createSupernova(star.x, star.y, originalMass * 0.7);

        } else if (originalMass > 8 * 1.989e30) {
            // Supernova -> neutronová hvězda
            star.type = 'neutronstar';
            star.mass = 1.4 * 1.989e30; // Chandrasekharova mez
            star.radius = 10000; // 10 km
            star.color = '#ffffff';

        } else {
            // Planetární mlhovina -> bílý trpaslík
            star.type = 'whitedwarf';
            star.mass = originalMass * 0.6;
            star.radius = 5000000; // 5000 km
            star.color = '#ffffcc';
        }

        console.log(`Hvězda zemřela: ${star.type}, hmotnost: ${star.mass.toExponential(2)} kg`);
    }

    // Supernova
    createSupernova(x, y, mass) {
        // Vytvoř masivní explozi
        const explosion = {
            x: x,
            y: y,
            energy: mass * this.c * this.c * 0.1, // 10% konverze hmotnosti
            time: 0,
            maxTime: 5.0,
            particles: []
        };

        // Mnoho částic pro supernovu
        for (let i = 0; i < 200; i++) {
            const angle = Math.random() * 2 * Math.PI;
            const speed = (Math.random() * 0.1 + 0.05) * this.c; // 5-15% rychlosti světla

            explosion.particles.push({
                x: x,
                y: y,
                vx: Math.cos(angle) * speed,
                vy: Math.sin(angle) * speed,
                life: 1.0,
                mass: mass / 200
            });
        }

        this.explosions.push(explosion);
    }

    // Evoluce planety
    evolvePlanet(planet, dt) {
        // Ochlazování planety
        const coolingRate = planet.temperature * 1e-12;
        planet.temperature = Math.max(2.7, planet.temperature - coolingRate * dt);

        // Atmosférická eroze
        if (planet.atmosphere) {
            const erosionRate = 1e-15;
            Object.keys(planet.atmosphere).forEach(gas => {
                planet.atmosphere[gas] *= (1 - erosionRate * dt);
            });
        }
    }

    // Evoluce černé díry
    evolveBlackHole(blackhole, dt) {
        // Hawkingovo vyzařování
        const hawkingPower = (this.h * this.c * this.c * this.c * this.c * this.c * this.c) /
                             (15360 * Math.PI * this.G * this.G * blackhole.mass * blackhole.mass);

        const massLoss = hawkingPower / (this.c * this.c) * dt;
        blackhole.mass -= massLoss;

        // Aktualizace poloměru
        blackhole.radius = this.calculateRadius(blackhole.mass, 'blackhole');

        // Pokud je černá díra příliš malá, "vypaří" se
        if (blackhole.mass < 1e15) { // kg
            blackhole.isDestroyed = true;
            this.createExplosion(blackhole.x, blackhole.y, blackhole.mass);
        }
    }

    // Aktualizace statistik
    updateStatistics() {
        this.totalMass = 0;
        this.totalEnergy = 0;

        this.objects.forEach(obj => {
            if (obj.isDestroyed) return;

            this.totalMass += obj.mass;

            // Kinetická energie: E = ½mv²
            const kineticEnergy = 0.5 * obj.mass * (obj.vx * obj.vx + obj.vy * obj.vy);

            // Potenciální energie (aproximace)
            let potentialEnergy = 0;
            this.objects.forEach(other => {
                if (other.id === obj.id || other.isDestroyed) return;
                const dx = other.x - obj.x;
                const dy = other.y - obj.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                potentialEnergy -= this.G * obj.mass * other.mass / distance;
            });

            this.totalEnergy += kineticEnergy + potentialEnergy * 0.5; // Dělíme 2, aby se nepočítalo dvakrát
        });
    }

    // Vyčištění zničených objektů
    cleanupDestroyed() {
        this.objects = this.objects.filter(obj => !obj.isDestroyed);
    }

    // Předpřipravené scénáře
    createSolarSystem(centerX, centerY) {
        // Slunce
        const sun = this.createObject('star', centerX, centerY, 1.989e30);

        // Planety s realistickými parametry
        const planets = [
            { name: 'Merkur', distance: 5.79e10, mass: 3.301e23, velocity: 47870 },
            { name: 'Venuše', distance: 1.082e11, mass: 4.867e24, velocity: 35020 },
            { name: 'Země', distance: 1.496e11, mass: 5.972e24, velocity: 29780 },
            { name: 'Mars', distance: 2.279e11, mass: 6.417e23, velocity: 24130 },
            { name: 'Jupiter', distance: 7.786e11, mass: 1.898e27, velocity: 13070 },
            { name: 'Saturn', distance: 1.432e12, mass: 5.683e26, velocity: 9690 },
            { name: 'Uran', distance: 2.867e12, mass: 8.681e25, velocity: 6810 },
            { name: 'Neptun', distance: 4.515e12, mass: 1.024e26, velocity: 5430 }
        ];

        planets.forEach(planetData => {
            const angle = Math.random() * 2 * Math.PI;
            const x = centerX + Math.cos(angle) * planetData.distance;
            const y = centerY + Math.sin(angle) * planetData.distance;

            // Orbitální rychlost kolmo na poloměr
            const vx = -Math.sin(angle) * planetData.velocity;
            const vy = Math.cos(angle) * planetData.velocity;

            this.createObject('planet', x, y, planetData.mass, vx, vy);
        });

        console.log('Sluneční soustava vytvořena');
    }

    createBinarySystem(centerX, centerY) {
        const separation = 2e11; // 200 milionů km
        const totalMass = 2 * 1.989e30;
        const orbitalVelocity = Math.sqrt(this.G * totalMass / separation) * 0.5;

        // Hvězda A
        this.createObject('star', centerX - separation/2, centerY, 1.989e30, 0, orbitalVelocity);

        // Hvězda B
        this.createObject('star', centerX + separation/2, centerY, 1.989e30, 0, -orbitalVelocity);

        console.log('Dvojhvězda vytvořena');
    }

    createGalaxy(centerX, centerY) {
        // Centrální černá díra
        this.createObject('blackhole', centerX, centerY, 4.1e36); // Sagittarius A*

        // Spirální ramena
        const armCount = 4;
        const starsPerArm = 50;

        for (let arm = 0; arm < armCount; arm++) {
            for (let i = 0; i < starsPerArm; i++) {
                const t = i / starsPerArm * 4 * Math.PI; // 4 otáčky
                const r = (i / starsPerArm) * 5e20; // 50 000 světelných let

                const armAngle = (arm / armCount) * 2 * Math.PI;
                const spiralAngle = armAngle + t;

                const x = centerX + r * Math.cos(spiralAngle);
                const y = centerY + r * Math.sin(spiralAngle);

                // Orbitální rychlost kolem centra
                const orbitalVel = Math.sqrt(this.G * 4.1e36 / r) * 0.1;
                const vx = -Math.sin(spiralAngle) * orbitalVel;
                const vy = Math.cos(spiralAngle) * orbitalVel;

                // Různé typy hvězd
                const starTypes = ['star', 'star', 'star', 'blackhole'];
                const type = starTypes[Math.floor(Math.random() * starTypes.length)];
                const mass = type === 'blackhole' ?
                    (Math.random() * 20 + 5) * 1.989e30 :
                    (Math.random() * 10 + 0.5) * 1.989e30;

                this.createObject(type, x, y, mass, vx, vy);
            }
        }

        console.log('Galaxie vytvořena');
    }

    // Uložení a načtení
    saveState() {
        const state = {
            objects: this.objects.map(obj => ({...obj, trail: []})), // Bez stop pro úsporu místa
            simulationTime: this.simulationTime,
            timeScale: this.timeScale,
            gravityMultiplier: this.gravityMultiplier,
            enableRelativity: this.enableRelativity,
            enableCollisions: this.enableCollisions,
            enableTrails: this.enableTrails
        };

        return JSON.stringify(state);
    }

    loadState(stateString) {
        try {
            const state = JSON.parse(stateString);

            this.objects = state.objects.map(obj => ({
                ...obj,
                trail: [],
                fx: 0,
                fy: 0,
                isDestroyed: false
            }));

            this.simulationTime = state.simulationTime || 0;
            this.timeScale = state.timeScale || 1;
            this.gravityMultiplier = state.gravityMultiplier || 1;
            this.enableRelativity = state.enableRelativity || false;
            this.enableCollisions = state.enableCollisions || true;
            this.enableTrails = state.enableTrails || true;

            console.log('Stav načten');
            return true;
        } catch (error) {
            console.error('Chyba při načítání stavu:', error);
            return false;
        }
    }
}
