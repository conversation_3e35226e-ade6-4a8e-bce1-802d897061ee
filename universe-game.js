// Universe Game Engine - <PERSON>n<PERSON> logika a rendering
class UniverseGame {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.minimap = document.getElementById('minimap');
        this.minimapCtx = this.minimap.getContext('2d');

        // Fyzikální engine
        this.physics = new UniversePhysics();

        // Herní stav
        this.isRunning = true;
        this.isPaused = false;
        this.currentMode = 'planet';
        this.selectedMass = 1e24;
        this.selectedVelocity = 0;

        // Kamera a zobrazení
        this.camera = {
            x: 0,
            y: 0,
            zoom: 1.0,
            targetZoom: 1.0,
            scale: 1e-9 // 1 pixel = 1 milion km
        };

        // Interakce
        this.mouse = {
            x: 0,
            y: 0,
            worldX: 0,
            worldY: 0,
            isDown: false,
            dragStart: null
        };

        // UI stav
        this.selectedObject = null;
        this.showTrails = true;
        this.showCollisions = true;
        this.showRelativity = false;

        // Performance
        this.lastTime = 0;
        this.fps = 60;
        this.frameCount = 0;
        this.lastFpsUpdate = 0;

        this.init();
    }

    init() {
        this.resizeCanvas();
        this.setupEventListeners();
        this.gameLoop();

        // Vytvoř základní scénář
        this.createSolarSystem();

        console.log('Universe Simulator inicializován');
    }

    resizeCanvas() {
        this.canvas.width = window.innerWidth;
        this.canvas.height = window.innerHeight;

        // Aktualizace kamery
        this.camera.x = this.canvas.width / 2;
        this.camera.y = this.canvas.height / 2;
    }

    setupEventListeners() {
        // Resize
        window.addEventListener('resize', () => this.resizeCanvas());

        // Mouse events
        this.canvas.addEventListener('mousedown', (e) => this.onMouseDown(e));
        this.canvas.addEventListener('mousemove', (e) => this.onMouseMove(e));
        this.canvas.addEventListener('mouseup', (e) => this.onMouseUp(e));
        this.canvas.addEventListener('wheel', (e) => this.onWheel(e));
        this.canvas.addEventListener('contextmenu', (e) => e.preventDefault());

        // Touch events pro mobily
        this.canvas.addEventListener('touchstart', (e) => this.onTouchStart(e));
        this.canvas.addEventListener('touchmove', (e) => this.onTouchMove(e));
        this.canvas.addEventListener('touchend', (e) => this.onTouchEnd(e));

        // Keyboard events
        document.addEventListener('keydown', (e) => this.onKeyDown(e));

        // Minimap click
        this.minimap.addEventListener('click', (e) => this.onMinimapClick(e));
    }

    // Mouse handling
    onMouseDown(e) {
        this.updateMousePosition(e);
        this.mouse.isDown = true;
        this.mouse.dragStart = { x: this.mouse.x, y: this.mouse.y };

        if (e.button === 0) { // Levé tlačítko
            this.handleLeftClick();
        } else if (e.button === 2) { // Pravé tlačítko
            this.handleRightClick();
        }
    }

    onMouseMove(e) {
        this.updateMousePosition(e);

        if (this.mouse.isDown && this.mouse.dragStart) {
            // Drag kamera
            const dx = this.mouse.x - this.mouse.dragStart.x;
            const dy = this.mouse.y - this.mouse.dragStart.y;

            this.camera.x += dx;
            this.camera.y += dy;

            this.mouse.dragStart = { x: this.mouse.x, y: this.mouse.y };
        }

        // Aktualizace world pozice
        this.updateWorldPosition();

        // Najdi objekt pod myší
        this.updateHoveredObject();
    }

    onMouseUp(e) {
        this.mouse.isDown = false;
        this.mouse.dragStart = null;
    }

    onWheel(e) {
        e.preventDefault();

        const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
        this.camera.targetZoom = Math.max(0.01, Math.min(100, this.camera.targetZoom * zoomFactor));
    }

    // Touch handling pro mobily
    onTouchStart(e) {
        e.preventDefault();
        if (e.touches.length === 1) {
            const touch = e.touches[0];
            this.onMouseDown({ clientX: touch.clientX, clientY: touch.clientY, button: 0 });
        }
    }

    onTouchMove(e) {
        e.preventDefault();
        if (e.touches.length === 1) {
            const touch = e.touches[0];
            this.onMouseMove({ clientX: touch.clientX, clientY: touch.clientY });
        }
    }

    onTouchEnd(e) {
        e.preventDefault();
        this.onMouseUp(e);
    }

    // Keyboard handling
    onKeyDown(e) {
        switch(e.key) {
            case ' ':
                e.preventDefault();
                this.togglePause();
                break;
            case 'r':
            case 'R':
                this.resetCamera();
                break;
            case '1': this.setMode('planet'); break;
            case '2': this.setMode('star'); break;
            case '3': this.setMode('asteroid'); break;
            case '4': this.setMode('blackhole'); break;
            case '5': this.setMode('ship'); break;
            case 'Delete':
                this.clearUniverse();
                break;
        }
    }

    // Minimap click
    onMinimapClick(e) {
        const rect = this.minimap.getBoundingClientRect();
        const x = (e.clientX - rect.left) / rect.width;
        const y = (e.clientY - rect.top) / rect.height;

        // Přesun kamery na kliknuté místo
        this.camera.x = this.canvas.width / 2;
        this.camera.y = this.canvas.height / 2;

        // TODO: Implementovat správný přepočet souřadnic
    }

    updateMousePosition(e) {
        const rect = this.canvas.getBoundingClientRect();
        this.mouse.x = e.clientX - rect.left;
        this.mouse.y = e.clientY - rect.top;
    }

    updateWorldPosition() {
        // Převod screen souřadnic na world souřadnice
        this.mouse.worldX = (this.mouse.x - this.camera.x) / (this.camera.zoom * this.camera.scale);
        this.mouse.worldY = (this.mouse.y - this.camera.y) / (this.camera.zoom * this.camera.scale);
    }

    updateHoveredObject() {
        this.selectedObject = null;

        this.physics.objects.forEach(obj => {
            const screenX = this.camera.x + obj.x * this.camera.zoom * this.camera.scale;
            const screenY = this.camera.y + obj.y * this.camera.zoom * this.camera.scale;
            const screenRadius = Math.max(3, obj.radius * this.camera.zoom * this.camera.scale);

            const dx = this.mouse.x - screenX;
            const dy = this.mouse.y - screenY;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance < screenRadius + 10) {
                this.selectedObject = obj;
            }
        });

        this.updateObjectInfo();
    }

    updateObjectInfo() {
        const infoDiv = document.getElementById('object-info');

        if (this.selectedObject) {
            const obj = this.selectedObject;
            const content = `
                <strong>${obj.type.toUpperCase()}</strong><br>
                Hmotnost: ${obj.mass.toExponential(2)} kg<br>
                Poloměr: ${this.formatDistance(obj.radius)}<br>
                Rychlost: ${this.formatVelocity(Math.sqrt(obj.vx*obj.vx + obj.vy*obj.vy))}<br>
                Teplota: ${obj.temperature.toFixed(1)} K<br>
                ${obj.luminosity > 0 ? `Svítivost: ${obj.luminosity.toExponential(2)} W<br>` : ''}
                Věk: ${this.formatTime(obj.age)}
            `;

            document.getElementById('info-content').innerHTML = content;
            infoDiv.style.display = 'block';
            infoDiv.style.left = this.mouse.x + 15 + 'px';
            infoDiv.style.top = this.mouse.y + 15 + 'px';
        } else {
            infoDiv.style.display = 'none';
        }
    }

    handleLeftClick() {
        if (this.currentMode === 'delete') {
            this.deleteObjectAtMouse();
        } else {
            this.createObjectAtMouse();
        }
    }

    handleRightClick() {
        // Pravé tlačítko - kontextové menu nebo speciální akce
        if (this.selectedObject) {
            this.showObjectMenu(this.selectedObject);
        }
    }

    createObjectAtMouse() {
        const angle = Math.random() * 2 * Math.PI;
        const vx = Math.cos(angle) * this.selectedVelocity * 1000; // km/s -> m/s
        const vy = Math.sin(angle) * this.selectedVelocity * 1000;

        const obj = this.physics.createObject(
            this.currentMode,
            this.mouse.worldX,
            this.mouse.worldY,
            this.selectedMass,
            vx,
            vy
        );

        console.log(`Vytvořen ${this.currentMode}: hmotnost ${this.selectedMass.toExponential(2)} kg`);
    }

    deleteObjectAtMouse() {
        if (this.selectedObject) {
            this.selectedObject.isDestroyed = true;
            console.log(`Smazán ${this.selectedObject.type}`);
        }
    }

    showObjectMenu(obj) {
        // TODO: Implementovat kontextové menu
        console.log(`Menu pro ${obj.type}:`, obj);
    }

    // Hlavní herní smyčka
    gameLoop(currentTime = 0) {
        const deltaTime = (currentTime - this.lastTime) / 1000;
        this.lastTime = currentTime;

        // FPS počítání
        this.frameCount++;
        if (currentTime - this.lastFpsUpdate > 1000) {
            this.fps = this.frameCount;
            this.frameCount = 0;
            this.lastFpsUpdate = currentTime;
        }

        if (this.isRunning && !this.isPaused) {
            // Aktualizace fyziky
            this.physics.update(deltaTime);

            // Smooth zoom
            this.camera.zoom += (this.camera.targetZoom - this.camera.zoom) * 0.1;
        }

        // Rendering
        this.render();

        // Aktualizace UI
        this.updateUI();

        requestAnimationFrame((time) => this.gameLoop(time));
    }

    render() {
        // Vyčištění canvasu
        this.ctx.fillStyle = '#000011';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Hvězdy na pozadí
        this.renderStarField();

        // Objekty
        this.renderObjects();

        // Stopy
        if (this.showTrails) {
            this.renderTrails();
        }

        // Exploze
        this.renderExplosions();

        // UI overlay
        this.renderUI();

        // Minimap
        this.renderMinimap();
    }

    renderStarField() {
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';

        // Statické hvězdy na pozadí
        for (let i = 0; i < 200; i++) {
            const x = (i * 1234567) % this.canvas.width;
            const y = (i * 7654321) % this.canvas.height;
            const size = Math.random() * 2;

            this.ctx.beginPath();
            this.ctx.arc(x, y, size, 0, 2 * Math.PI);
            this.ctx.fill();
        }
    }

    renderObjects() {
        this.physics.objects.forEach(obj => {
            if (obj.isDestroyed) return;

            const screenX = this.camera.x + obj.x * this.camera.zoom * this.camera.scale;
            const screenY = this.camera.y + obj.y * this.camera.zoom * this.camera.scale;
            const screenRadius = Math.max(2, obj.radius * this.camera.zoom * this.camera.scale);

            // Culling - nekreslí objekty mimo obrazovku
            if (screenX < -screenRadius || screenX > this.canvas.width + screenRadius ||
                screenY < -screenRadius || screenY > this.canvas.height + screenRadius) {
                return;
            }

            this.ctx.save();

            // Glow efekt pro hvězdy
            if (obj.type === 'star' && obj.luminosity > 0) {
                const glowRadius = screenRadius * 3;
                const gradient = this.ctx.createRadialGradient(
                    screenX, screenY, screenRadius,
                    screenX, screenY, glowRadius
                );
                gradient.addColorStop(0, obj.color + '80');
                gradient.addColorStop(1, obj.color + '00');

                this.ctx.fillStyle = gradient;
                this.ctx.beginPath();
                this.ctx.arc(screenX, screenY, glowRadius, 0, 2 * Math.PI);
                this.ctx.fill();
            }

            // Hlavní objekt
            this.ctx.fillStyle = obj.color;
            this.ctx.beginPath();

            if (obj.type === 'blackhole') {
                // Černá díra - event horizon
                this.ctx.fillStyle = '#000000';
                this.ctx.arc(screenX, screenY, screenRadius, 0, 2 * Math.PI);
                this.ctx.fill();

                // Accretion disk
                this.ctx.strokeStyle = '#ff6600';
                this.ctx.lineWidth = 2;
                this.ctx.beginPath();
                this.ctx.arc(screenX, screenY, screenRadius * 2, 0, 2 * Math.PI);
                this.ctx.stroke();

            } else {
                this.ctx.arc(screenX, screenY, screenRadius, 0, 2 * Math.PI);
                this.ctx.fill();
            }

            // Outline pro vybraný objekt
            if (obj === this.selectedObject) {
                this.ctx.strokeStyle = '#00ffff';
                this.ctx.lineWidth = 2;
                this.ctx.beginPath();
                this.ctx.arc(screenX, screenY, screenRadius + 5, 0, 2 * Math.PI);
                this.ctx.stroke();
            }

            // Velocity vector
            if (screenRadius > 5) {
                const velScale = 1e-6;
                const velX = obj.vx * velScale * this.camera.zoom;
                const velY = obj.vy * velScale * this.camera.zoom;

                if (Math.abs(velX) > 1 || Math.abs(velY) > 1) {
                    this.ctx.strokeStyle = '#00ff00';
                    this.ctx.lineWidth = 1;
                    this.ctx.beginPath();
                    this.ctx.moveTo(screenX, screenY);
                    this.ctx.lineTo(screenX + velX, screenY + velY);
                    this.ctx.stroke();

                    // Šipka
                    const angle = Math.atan2(velY, velX);
                    const arrowSize = 5;
                    this.ctx.beginPath();
                    this.ctx.moveTo(screenX + velX, screenY + velY);
                    this.ctx.lineTo(
                        screenX + velX - arrowSize * Math.cos(angle - 0.3),
                        screenY + velY - arrowSize * Math.sin(angle - 0.3)
                    );
                    this.ctx.moveTo(screenX + velX, screenY + velY);
                    this.ctx.lineTo(
                        screenX + velX - arrowSize * Math.cos(angle + 0.3),
                        screenY + velY - arrowSize * Math.sin(angle + 0.3)
                    );
                    this.ctx.stroke();
                }
            }

            this.ctx.restore();
        });
    }

    renderTrails() {
        this.physics.objects.forEach(obj => {
            if (obj.isDestroyed || obj.trail.length < 2) return;

            this.ctx.strokeStyle = obj.color + '60';
            this.ctx.lineWidth = 1;
            this.ctx.beginPath();

            let first = true;
            obj.trail.forEach(point => {
                const screenX = this.camera.x + point.x * this.camera.zoom * this.camera.scale;
                const screenY = this.camera.y + point.y * this.camera.zoom * this.camera.scale;

                if (first) {
                    this.ctx.moveTo(screenX, screenY);
                    first = false;
                } else {
                    this.ctx.lineTo(screenX, screenY);
                }
            });

            this.ctx.stroke();
        });
    }

    renderExplosions() {
        this.physics.explosions.forEach(explosion => {
            explosion.particles.forEach(particle => {
                if (particle.life <= 0) return;

                const screenX = this.camera.x + particle.x * this.camera.zoom * this.camera.scale;
                const screenY = this.camera.y + particle.y * this.camera.zoom * this.camera.scale;

                this.ctx.fillStyle = `rgba(255, ${100 + particle.life * 155}, 0, ${particle.life})`;
                this.ctx.beginPath();
                this.ctx.arc(screenX, screenY, particle.life * 3, 0, 2 * Math.PI);
                this.ctx.fill();
            });
        });
    }

    renderUI() {
        // Crosshair
        this.ctx.strokeStyle = '#00ffff';
        this.ctx.lineWidth = 1;
        this.ctx.beginPath();
        this.ctx.moveTo(this.mouse.x - 10, this.mouse.y);
        this.ctx.lineTo(this.mouse.x + 10, this.mouse.y);
        this.ctx.moveTo(this.mouse.x, this.mouse.y - 10);
        this.ctx.lineTo(this.mouse.x, this.mouse.y + 10);
        this.ctx.stroke();

        // Zoom indikátor
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        this.ctx.font = '12px Courier New';
        this.ctx.fillText(`Zoom: ${this.camera.zoom.toFixed(2)}x`, 10, this.canvas.height - 10);
    }

    renderMinimap() {
        const ctx = this.minimapCtx;
        const width = this.minimap.width;
        const height = this.minimap.height;

        // Vyčištění
        ctx.fillStyle = '#000022';
        ctx.fillRect(0, 0, width, height);

        // Najdi hranice všech objektů
        let minX = Infinity, maxX = -Infinity;
        let minY = Infinity, maxY = -Infinity;

        this.physics.objects.forEach(obj => {
            if (obj.isDestroyed) return;
            minX = Math.min(minX, obj.x);
            maxX = Math.max(maxX, obj.x);
            minY = Math.min(minY, obj.y);
            maxY = Math.max(maxY, obj.y);
        });

        if (minX === Infinity) return;

        const rangeX = maxX - minX || 1;
        const rangeY = maxY - minY || 1;
        const scale = Math.min(width / rangeX, height / rangeY) * 0.8;

        // Objekty na minimapě
        this.physics.objects.forEach(obj => {
            if (obj.isDestroyed) return;

            const x = (obj.x - minX) * scale + width * 0.1;
            const y = (obj.y - minY) * scale + height * 0.1;
            const radius = Math.max(1, Math.log10(obj.mass / 1e20));

            ctx.fillStyle = obj.color;
            ctx.beginPath();
            ctx.arc(x, y, radius, 0, 2 * Math.PI);
            ctx.fill();
        });

        // Viewport indikátor
        ctx.strokeStyle = '#00ffff';
        ctx.lineWidth = 1;
        ctx.strokeRect(width * 0.4, height * 0.4, width * 0.2, height * 0.2);
    }

    updateUI() {
        // Aktualizace hodnot v UI
        document.getElementById('time-display').textContent = this.formatTime(this.physics.simulationTime);
        document.getElementById('object-count').textContent = this.physics.objects.length;
        document.getElementById('total-mass').textContent = this.formatMass(this.physics.totalMass);
        document.getElementById('total-energy').textContent = this.formatEnergy(this.physics.totalEnergy);
        document.getElementById('fps-display').textContent = this.fps;
        document.getElementById('delta-time').textContent = (1/this.fps).toFixed(3) + ' s';
        document.getElementById('scale-display').textContent = `1:${(1/this.camera.scale).toExponential(1)}`;

        // Varování
        const warningsDiv = document.getElementById('warnings');
        const unstableOrbits = this.detectUnstableOrbits();
        if (unstableOrbits > 0) {
            warningsDiv.textContent = `⚠️ ${unstableOrbits} nestabilních orbit detekováno!`;
            warningsDiv.style.display = 'block';
        } else {
            warningsDiv.style.display = 'none';
        }
    }

    detectUnstableOrbits() {
        let unstable = 0;

        this.physics.objects.forEach(obj => {
            if (obj.isDestroyed) return;

            const velocity = Math.sqrt(obj.vx * obj.vx + obj.vy * obj.vy);
            const escapeVelocity = Math.sqrt(2 * this.physics.G * obj.mass / Math.max(obj.radius * 10, 1e6));

            if (velocity > escapeVelocity * 2) {
                unstable++;
            }
        });

        return unstable;
    }

    // Utility funkce pro formátování
    formatTime(seconds) {
        if (seconds < 60) return seconds.toFixed(2) + ' s';
        if (seconds < 3600) return (seconds / 60).toFixed(1) + ' min';
        if (seconds < 86400) return (seconds / 3600).toFixed(1) + ' h';
        if (seconds < 31536000) return (seconds / 86400).toFixed(1) + ' dní';
        return (seconds / 31536000).toFixed(1) + ' let';
    }

    formatMass(mass) {
        if (mass < 1e3) return mass.toFixed(2) + ' kg';
        if (mass < 1e6) return (mass / 1e3).toFixed(2) + ' t';
        if (mass < 1e30) return (mass / 1e24).toFixed(2) + ' Yg';
        return (mass / 1.989e30).toFixed(2) + ' M☉';
    }

    formatEnergy(energy) {
        if (energy < 1e3) return energy.toFixed(2) + ' J';
        if (energy < 1e6) return (energy / 1e3).toFixed(2) + ' kJ';
        if (energy < 1e9) return (energy / 1e6).toFixed(2) + ' MJ';
        if (energy < 1e12) return (energy / 1e9).toFixed(2) + ' GJ';
        return (energy / 1e12).toFixed(2) + ' TJ';
    }

    formatDistance(distance) {
        if (distance < 1e3) return distance.toFixed(0) + ' m';
        if (distance < 1e6) return (distance / 1e3).toFixed(1) + ' km';
        if (distance < 1.496e11) return (distance / 1e6).toFixed(1) + ' Mm';
        if (distance < 9.461e15) return (distance / 1.496e11).toFixed(2) + ' AU';
        return (distance / 9.461e15).toFixed(2) + ' ly';
    }

    formatVelocity(velocity) {
        if (velocity < 1e3) return velocity.toFixed(0) + ' m/s';
        if (velocity < 3e8) return (velocity / 1e3).toFixed(1) + ' km/s';
        return (velocity / 299792458).toFixed(3) + ' c';
    }

    // Herní funkce
    setMode(mode) {
        this.currentMode = mode;

        // Aktualizace UI
        document.querySelectorAll('[id^="mode-"]').forEach(btn => btn.classList.remove('active'));
        document.getElementById(`mode-${mode}`).classList.add('active');

        console.log(`Režim změněn na: ${mode}`);
    }

    togglePause() {
        this.isPaused = !this.isPaused;
        console.log(this.isPaused ? 'Simulace pozastavena' : 'Simulace obnovena');
    }

    resetCamera() {
        this.camera.x = this.canvas.width / 2;
        this.camera.y = this.canvas.height / 2;
        this.camera.zoom = 1.0;
        this.camera.targetZoom = 1.0;
    }

    clearUniverse() {
        this.physics.objects = [];
        this.physics.explosions = [];
        this.physics.simulationTime = 0;
        console.log('Vesmír vymazán');
    }

    createSolarSystem() {
        this.physics.createSolarSystem(0, 0);
    }

    createBinarySystem() {
        this.physics.createBinarySystem(0, 0);
    }

    createGalaxy() {
        this.physics.createGalaxy(0, 0);
    }

    toggleTrails() {
        this.showTrails = !this.showTrails;
        this.physics.enableTrails = this.showTrails;
        console.log(`Stopy: ${this.showTrails ? 'zapnuto' : 'vypnuto'}`);
    }

    toggleCollisions() {
        this.showCollisions = !this.showCollisions;
        this.physics.enableCollisions = this.showCollisions;
        console.log(`Kolize: ${this.showCollisions ? 'zapnuto' : 'vypnuto'}`);
    }

    toggleRelativity() {
        this.showRelativity = !this.showRelativity;
        this.physics.enableRelativity = this.showRelativity;
        console.log(`Relativita: ${this.showRelativity ? 'zapnuto' : 'vypnuto'}`);
    }

    pauseSimulation() {
        this.togglePause();
    }

    saveUniverse() {
        const state = this.physics.saveState();
        const blob = new Blob([state], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `universe_${Date.now()}.json`;
        a.click();

        URL.revokeObjectURL(url);
        console.log('Vesmír uložen');
    }

    loadUniverse() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';

        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    if (this.physics.loadState(e.target.result)) {
                        console.log('Vesmír načten');
                    } else {
                        alert('Chyba při načítání souboru');
                    }
                };
                reader.readAsText(file);
            }
        };

        input.click();
    }
}

// Globální funkce pro UI
let game;

function setMode(mode) {
    if (game) game.setMode(mode);
}

function updateMass(value) {
    if (game) {
        game.selectedMass = Math.pow(10, parseFloat(value));
        document.getElementById('mass-value').textContent = game.selectedMass.toExponential(1) + ' kg';
    }
}

function updateVelocity(value) {
    if (game) {
        game.selectedVelocity = parseFloat(value);
        document.getElementById('velocity-value').textContent = value + ' km/s';
    }
}

function updateTimeScale(value) {
    if (game) {
        game.physics.timeScale = parseFloat(value);
        document.getElementById('time-scale-value').textContent = value + 'x';
    }
}

function updateZoom(value) {
    if (game) {
        game.camera.targetZoom = parseFloat(value);
        document.getElementById('zoom-value').textContent = value + 'x';
    }
}

function updateGravity(value) {
    if (game) {
        game.physics.gravityMultiplier = parseFloat(value) / 100;
        document.getElementById('gravity-value').textContent = value + '%';
    }
}

function clearUniverse() { if (game) game.clearUniverse(); }
function createSolarSystem() { if (game) game.createSolarSystem(); }
function createBinarySystem() { if (game) game.createBinarySystem(); }
function createGalaxy() { if (game) game.createGalaxy(); }
function toggleTrails() { if (game) game.toggleTrails(); }
function toggleCollisions() { if (game) game.toggleCollisions(); }
function toggleRelativity() { if (game) game.toggleRelativity(); }
function pauseSimulation() { if (game) game.pauseSimulation(); }
function saveUniverse() { if (game) game.saveUniverse(); }
function loadUniverse() { if (game) game.loadUniverse(); }

// Inicializace při načtení stránky
document.addEventListener('DOMContentLoaded', () => {
    try {
        game = new UniverseGame();
        console.log('Universe Simulator spuštěn!');

        // Zobrazení instrukcí
        setTimeout(() => {
            console.log(`
🌌 UNIVERSE SIMULATOR - Instrukce:

🎮 OVLÁDÁNÍ:
• Levé tlačítko myši - Vytvoření objektu
• Pravé tlačítko myši - Kontextové menu
• Kolečko myši - Zoom
• Tažení myší - Pohyb kamery
• Mezerník - Pauza/Pokračování
• R - Reset kamery
• 1-5 - Rychlý výběr objektu

🔬 FYZIKA:
• Realistická gravitace (Newton + Einstein)
• Kolize a fúze objektů
• Hvězdná evoluce a smrt
• Relativistické efekty
• Hawkingovo vyzařování

🌟 SCÉNÁŘE:
• Sluneční soustava
• Dvojhvězda
• Galaxie

Užijte si brutální vesmírnou simulaci! 🚀
            `);
        }, 2000);

    } catch (error) {
        console.error('Chyba při inicializaci:', error);
        alert('Chyba při spuštění simulace. Zkuste obnovit stránku.');
    }
});
