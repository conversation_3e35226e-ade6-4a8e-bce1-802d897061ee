<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌌 3D Universe Demo - Rychlá Verze</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Courier New', monospace; 
            background: #000; 
            color: #fff; 
            overflow: hidden; 
        }
        #container { width: 100vw; height: 100vh; position: relative; }
        #canvas3d { display: block; background: #000011; }
        .hud {
            position: absolute; top: 0; left: 0; width: 100%; height: 100%;
            pointer-events: none; z-index: 100;
        }
        .panel {
            position: absolute; background: rgba(0,0,0,0.9); padding: 15px;
            border-radius: 10px; border: 1px solid #00ffff; pointer-events: auto;
        }
        .top-left { top: 20px; left: 20px; }
        .top-right { top: 20px; right: 20px; }
        .bottom { bottom: 20px; left: 20px; right: 20px; text-align: center; }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none; color: white; padding: 10px 15px; margin: 5px;
            border-radius: 20px; cursor: pointer; font-size: 12px;
            transition: all 0.3s ease;
        }
        .btn:hover { transform: translateY(-2px); }
        .btn.active { background: linear-gradient(45deg, #ff6b6b, #ee5a24); }
        .title { color: #00ffff; font-size: 16px; margin-bottom: 10px; text-align: center; }
        .info { font-size: 11px; margin: 5px 0; }
        .crosshair {
            position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
            width: 20px; height: 20px; border: 1px solid #00ffff; border-radius: 50%;
            pointer-events: none; opacity: 0.7;
        }
    </style>
</head>
<body>
    <div id="container">
        <canvas id="canvas3d"></canvas>
        
        <div class="hud">
            <div class="crosshair"></div>
            
            <div class="panel top-left">
                <div class="title">🎮 Ovládání</div>
                <button class="btn active" onclick="setMode('planet')">🪐 Planeta</button>
                <button class="btn" onclick="setMode('star')">⭐ Hvězda</button>
                <button class="btn" onclick="setMode('blackhole')">🕳️ Černá díra</button>
                <button class="btn" onclick="setMode('asteroid')">☄️ Asteroid</button>
                <div class="info">Levé tlačítko: Vytvoř objekt</div>
                <div class="info">Pravé tlačítko: Rotace kamery</div>
                <div class="info">Kolečko: Zoom</div>
            </div>

            <div class="panel top-right">
                <div class="title">📊 Statistiky</div>
                <div class="info">FPS: <span id="fps">60</span></div>
                <div class="info">Objekty: <span id="objects">0</span></div>
                <div class="info">Čas: <span id="time">0.0s</span></div>
                <button class="btn" onclick="togglePhysics()" id="pause-btn">⏸️ Pauza</button>
                <button class="btn" onclick="clearAll()">💥 Vymazat</button>
            </div>

            <div class="panel bottom">
                <div class="title">🌟 Rychlé Scénáře</div>
                <button class="btn" onclick="createSolarSystem()">☀️ Sluneční soustava</button>
                <button class="btn" onclick="createBinarySystem()">⭐⭐ Dvojhvězda</button>
                <button class="btn" onclick="createGalaxy()">🌌 Galaxie</button>
                <button class="btn" onclick="createAsteroidField()">☄️ Asteroidy</button>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script>
        // Jednoduchá 3D vesmírná simulace
        class SimpleUniverse {
            constructor() {
                this.scene = new THREE.Scene();
                this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 10000);
                this.renderer = new THREE.WebGLRenderer({ canvas: document.getElementById('canvas3d'), antialias: true });
                
                this.objects = [];
                this.currentMode = 'planet';
                this.isRunning = true;
                this.time = 0;
                this.fps = 60;
                this.frameCount = 0;
                this.lastTime = 0;
                
                this.init();
            }
            
            init() {
                // Renderer setup
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                this.renderer.setClearColor(0x000011);
                
                // Camera position
                this.camera.position.set(0, 50, 100);
                this.camera.lookAt(0, 0, 0);
                
                // Lighting
                const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
                this.scene.add(ambientLight);
                
                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.7);
                directionalLight.position.set(100, 100, 50);
                this.scene.add(directionalLight);
                
                // Star field
                this.createStarField();
                
                // Controls
                this.setupControls();
                
                // Events
                this.renderer.domElement.addEventListener('click', (e) => this.onClick(e));
                window.addEventListener('resize', () => this.onResize());
                
                // Start animation
                this.animate();
                
                // Create initial solar system
                setTimeout(() => this.createSolarSystem(), 500);
                
                console.log('🌌 Simple Universe Demo načten!');
            }
            
            createStarField() {
                const starGeometry = new THREE.BufferGeometry();
                const starCount = 5000;
                const positions = new Float32Array(starCount * 3);
                
                for (let i = 0; i < starCount; i++) {
                    const radius = 2000 + Math.random() * 3000;
                    const theta = Math.random() * Math.PI * 2;
                    const phi = Math.acos(2 * Math.random() - 1);
                    
                    positions[i * 3] = radius * Math.sin(phi) * Math.cos(theta);
                    positions[i * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
                    positions[i * 3 + 2] = radius * Math.cos(phi);
                }
                
                starGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
                const starMaterial = new THREE.PointsMaterial({ color: 0xffffff, size: 1 });
                const stars = new THREE.Points(starGeometry, starMaterial);
                this.scene.add(stars);
            }
            
            setupControls() {
                let isRotating = false;
                let lastMouse = { x: 0, y: 0 };
                
                this.renderer.domElement.addEventListener('mousedown', (e) => {
                    if (e.button === 2) { // Right button
                        isRotating = true;
                        lastMouse = { x: e.clientX, y: e.clientY };
                    }
                });
                
                this.renderer.domElement.addEventListener('mousemove', (e) => {
                    if (isRotating) {
                        const deltaX = e.clientX - lastMouse.x;
                        const deltaY = e.clientY - lastMouse.y;
                        
                        // Rotate camera around origin
                        const spherical = new THREE.Spherical();
                        spherical.setFromVector3(this.camera.position);
                        spherical.theta -= deltaX * 0.01;
                        spherical.phi += deltaY * 0.01;
                        spherical.phi = Math.max(0.1, Math.min(Math.PI - 0.1, spherical.phi));
                        
                        this.camera.position.setFromSpherical(spherical);
                        this.camera.lookAt(0, 0, 0);
                        
                        lastMouse = { x: e.clientX, y: e.clientY };
                    }
                });
                
                this.renderer.domElement.addEventListener('mouseup', () => {
                    isRotating = false;
                });
                
                this.renderer.domElement.addEventListener('wheel', (e) => {
                    const distance = this.camera.position.length();
                    const factor = e.deltaY > 0 ? 1.1 : 0.9;
                    this.camera.position.multiplyScalar(factor);
                });
                
                this.renderer.domElement.addEventListener('contextmenu', (e) => e.preventDefault());
            }
            
            onClick(event) {
                if (event.button !== 0) return; // Only left click
                
                // Create object at click position
                const mouse = new THREE.Vector2();
                mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
                mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
                
                const raycaster = new THREE.Raycaster();
                raycaster.setFromCamera(mouse, this.camera);
                
                const distance = 50;
                const position = new THREE.Vector3();
                raycaster.ray.at(distance, position);
                
                this.createObject(this.currentMode, position);
            }
            
            createObject(type, position) {
                let geometry, material, mass;
                
                switch(type) {
                    case 'planet':
                        geometry = new THREE.SphereGeometry(2, 16, 16);
                        material = new THREE.MeshPhongMaterial({ color: 0x4a90e2 });
                        mass = 1e24;
                        break;
                    case 'star':
                        geometry = new THREE.SphereGeometry(4, 16, 16);
                        material = new THREE.MeshBasicMaterial({ color: 0xffff00 });
                        mass = 1.989e30;
                        break;
                    case 'blackhole':
                        geometry = new THREE.SphereGeometry(1, 16, 16);
                        material = new THREE.MeshBasicMaterial({ color: 0x000000 });
                        mass = 4.1e36;
                        break;
                    case 'asteroid':
                        geometry = new THREE.DodecahedronGeometry(1, 0);
                        material = new THREE.MeshPhongMaterial({ color: 0x8b4513 });
                        mass = 1e18;
                        break;
                }
                
                const mesh = new THREE.Mesh(geometry, material);
                mesh.position.copy(position);
                this.scene.add(mesh);
                
                const obj = {
                    mesh: mesh,
                    position: position.clone(),
                    velocity: new THREE.Vector3(),
                    mass: mass,
                    type: type
                };
                
                this.objects.push(obj);
                console.log(`✨ Vytvořen ${type} na pozici (${position.x.toFixed(1)}, ${position.y.toFixed(1)}, ${position.z.toFixed(1)})`);
            }
            
            updatePhysics(dt) {
                if (!this.isRunning) return;
                
                // Simple gravity simulation
                const G = 6.67430e-11 * 1e15; // Scaled for visualization
                
                this.objects.forEach(obj1 => {
                    obj1.force = new THREE.Vector3();
                    
                    this.objects.forEach(obj2 => {
                        if (obj1 === obj2) return;
                        
                        const distance = obj1.position.distanceTo(obj2.position);
                        if (distance < 0.1) return;
                        
                        const force = G * obj1.mass * obj2.mass / (distance * distance);
                        const direction = new THREE.Vector3().subVectors(obj2.position, obj1.position).normalize();
                        
                        obj1.force.add(direction.multiplyScalar(force));
                    });
                    
                    // Update velocity and position
                    const acceleration = obj1.force.divideScalar(obj1.mass);
                    obj1.velocity.add(acceleration.multiplyScalar(dt));
                    obj1.position.add(obj1.velocity.clone().multiplyScalar(dt));
                    
                    // Update mesh position
                    obj1.mesh.position.copy(obj1.position);
                    
                    // Rotate objects
                    obj1.mesh.rotation.x += 0.01;
                    obj1.mesh.rotation.y += 0.02;
                });
            }
            
            animate() {
                requestAnimationFrame(() => this.animate());
                
                const currentTime = performance.now();
                const deltaTime = (currentTime - this.lastTime) / 1000;
                this.lastTime = currentTime;
                this.time += deltaTime;
                
                // FPS calculation
                this.frameCount++;
                if (this.frameCount % 60 === 0) {
                    this.fps = Math.round(1 / deltaTime);
                }
                
                // Physics update
                this.updatePhysics(deltaTime);
                
                // Render
                this.renderer.render(this.scene, this.camera);
                
                // Update UI
                document.getElementById('fps').textContent = this.fps;
                document.getElementById('objects').textContent = this.objects.length;
                document.getElementById('time').textContent = this.time.toFixed(1) + 's';
            }
            
            onResize() {
                this.camera.aspect = window.innerWidth / window.innerHeight;
                this.camera.updateProjectionMatrix();
                this.renderer.setSize(window.innerWidth, window.innerHeight);
            }
            
            // Scenario methods
            createSolarSystem() {
                this.clearAll();
                
                // Sun
                this.createObject('star', new THREE.Vector3(0, 0, 0));
                
                // Planets
                const planets = [
                    { distance: 15, velocity: 8 },
                    { distance: 25, velocity: 6 },
                    { distance: 35, velocity: 5 },
                    { distance: 50, velocity: 3 }
                ];
                
                planets.forEach(p => {
                    const angle = Math.random() * Math.PI * 2;
                    const pos = new THREE.Vector3(Math.cos(angle) * p.distance, 0, Math.sin(angle) * p.distance);
                    const planet = this.createObject('planet', pos);
                    
                    // Set orbital velocity
                    if (this.objects.length > 0) {
                        const lastObj = this.objects[this.objects.length - 1];
                        lastObj.velocity.set(-Math.sin(angle) * p.velocity, 0, Math.cos(angle) * p.velocity);
                    }
                });
                
                console.log('☀️ Sluneční soustava vytvořena!');
            }
            
            createBinarySystem() {
                this.clearAll();
                
                this.createObject('star', new THREE.Vector3(-20, 0, 0));
                this.createObject('star', new THREE.Vector3(20, 0, 0));
                
                if (this.objects.length >= 2) {
                    this.objects[0].velocity.set(0, 0, 3);
                    this.objects[1].velocity.set(0, 0, -3);
                }
                
                console.log('⭐⭐ Dvojhvězda vytvořena!');
            }
            
            createGalaxy() {
                this.clearAll();
                
                // Central black hole
                this.createObject('blackhole', new THREE.Vector3(0, 0, 0));
                
                // Spiral arms
                for (let i = 0; i < 50; i++) {
                    const t = i / 50 * 4 * Math.PI;
                    const r = i * 2 + 10;
                    const angle = t * 0.3;
                    
                    const pos = new THREE.Vector3(r * Math.cos(angle), (Math.random() - 0.5) * 5, r * Math.sin(angle));
                    const star = this.createObject('star', pos);
                    
                    if (this.objects.length > 0) {
                        const lastObj = this.objects[this.objects.length - 1];
                        const orbitalVel = Math.sqrt(20 / r);
                        lastObj.velocity.set(-Math.sin(angle) * orbitalVel, 0, Math.cos(angle) * orbitalVel);
                    }
                }
                
                console.log('🌌 Galaxie vytvořena!');
            }
            
            createAsteroidField() {
                this.clearAll();
                
                // Central star
                this.createObject('star', new THREE.Vector3(0, 0, 0));
                
                // Asteroid belt
                for (let i = 0; i < 30; i++) {
                    const angle = Math.random() * Math.PI * 2;
                    const distance = 30 + Math.random() * 20;
                    const pos = new THREE.Vector3(Math.cos(angle) * distance, (Math.random() - 0.5) * 5, Math.sin(angle) * distance);
                    
                    const asteroid = this.createObject('asteroid', pos);
                    
                    if (this.objects.length > 0) {
                        const lastObj = this.objects[this.objects.length - 1];
                        const orbitalVel = Math.sqrt(15 / distance) * (0.8 + Math.random() * 0.4);
                        lastObj.velocity.set(-Math.sin(angle) * orbitalVel, 0, Math.cos(angle) * orbitalVel);
                    }
                }
                
                console.log('☄️ Asteroidové pole vytvořeno!');
            }
            
            clearAll() {
                this.objects.forEach(obj => this.scene.remove(obj.mesh));
                this.objects = [];
                console.log('💥 Vesmír vymazán!');
            }
            
            togglePhysics() {
                this.isRunning = !this.isRunning;
                document.getElementById('pause-btn').textContent = this.isRunning ? '⏸️ Pauza' : '▶️ Pokračovat';
            }
            
            setMode(mode) {
                this.currentMode = mode;
                document.querySelectorAll('.btn').forEach(btn => btn.classList.remove('active'));
                event.target.classList.add('active');
                console.log(`🎮 Režim: ${mode}`);
            }
        }
        
        // Global functions
        let universe;
        
        function setMode(mode) { if (universe) universe.setMode(mode); }
        function togglePhysics() { if (universe) universe.togglePhysics(); }
        function clearAll() { if (universe) universe.clearAll(); }
        function createSolarSystem() { if (universe) universe.createSolarSystem(); }
        function createBinarySystem() { if (universe) universe.createBinarySystem(); }
        function createGalaxy() { if (universe) universe.createGalaxy(); }
        function createAsteroidField() { if (universe) universe.createAsteroidField(); }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            universe = new SimpleUniverse();
        });
    </script>
</body>
</html>
