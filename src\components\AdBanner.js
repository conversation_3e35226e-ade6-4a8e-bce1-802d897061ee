import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Linking,
  Alert,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialIcons';
import HapticFeedback from 'react-native-haptic-feedback';

// Services
import AdService from '../services/AdService';
import AnalyticsService from '../services/AnalyticsService';

const AdBanner = () => {
  const [currentAd, setCurrentAd] = useState(null);
  const [fadeAnim] = useState(new Animated.Value(1));
  const [shimmerAnim] = useState(new Animated.Value(0));

  const ads = [
    {
      id: 'spotify_premium',
      title: '🎧 Spotify Premium',
      subtitle: '3 měsíce zdarma pro nové uživatele!',
      colors: ['#1DB954', '#1ed760'],
      url: 'https://spotify.com/premium',
      category: 'music',
      cta: 'Získat zdarma',
    },
    {
      id: 'headspace_trial',
      title: '🧘‍♀️ Headspace',
      subtitle: 'Meditace pro lepší spánek a klid',
      colors: ['#FF6B35', '#F7931E'],
      url: 'https://headspace.com',
      category: 'wellness',
      cta: 'Vyzkoušet',
    },
    {
      id: 'audible_books',
      title: '📚 Audible',
      subtitle: 'Knihy o mindfulness a osobním rozvoji',
      colors: ['#FF9500', '#FFAD33'],
      url: 'https://audible.com',
      category: 'education',
      cta: 'Poslouchat',
    },
    {
      id: 'emma_mattress',
      title: '🛏️ Emma matrace',
      subtitle: 'Lepší spánek = lepší mentální zdraví',
      colors: ['#00C9A7', '#00E5C3'],
      url: 'https://emma-sleep.com',
      category: 'sleep',
      cta: 'Koupit',
    },
    {
      id: 'calm_app',
      title: '🌙 Calm',
      subtitle: 'Spánkové příběhy a relaxační hudba',
      colors: ['#2F80ED', '#56CCF2'],
      url: 'https://calm.com',
      category: 'wellness',
      cta: 'Stáhnout',
    },
  ];

  useEffect(() => {
    loadAd();
    startShimmerAnimation();
    
    // Rotace reklam každých 15 sekund
    const interval = setInterval(() => {
      rotateAd();
    }, 15000);

    return () => clearInterval(interval);
  }, []);

  const loadAd = async () => {
    try {
      // Získej personalizovanou reklamu na základě uživatelských dat
      const personalizedAd = await AdService.getPersonalizedAd();
      
      if (personalizedAd) {
        setCurrentAd(personalizedAd);
      } else {
        // Fallback na náhodnou reklamu
        const randomAd = ads[Math.floor(Math.random() * ads.length)];
        setCurrentAd(randomAd);
      }
    } catch (error) {
      console.error('Error loading ad:', error);
      // Fallback
      setCurrentAd(ads[0]);
    }
  };

  const rotateAd = () => {
    // Fade out
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      // Změň reklamu
      const currentIndex = ads.findIndex(ad => ad.id === currentAd?.id);
      const nextIndex = (currentIndex + 1) % ads.length;
      setCurrentAd(ads[nextIndex]);
      
      // Fade in
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    });
  };

  const startShimmerAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(shimmerAnim, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(shimmerAnim, {
          toValue: 0,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const handleAdPress = async () => {
    if (!currentAd) return;

    try {
      HapticFeedback.trigger('impactMedium');
      
      // Track ad click
      await AnalyticsService.trackEvent('ad_clicked', {
        ad_id: currentAd.id,
        category: currentAd.category,
      });
      
      // Track revenue (simulace)
      await AdService.trackAdClick(currentAd);
      
      // Zobraz poděkování
      Alert.alert(
        'Děkujeme za podporu! 💝',
        'Reklamy nám pomáhají udržovat MindFlow zdarma pro všechny.',
        [
          {
            text: 'Pokračovat',
            onPress: () => {
              // Otevři URL
              if (currentAd.url) {
                Linking.openURL(currentAd.url).catch(err => {
                  console.error('Error opening URL:', err);
                });
              }
            },
          },
          {
            text: 'Zavřít',
            style: 'cancel',
          },
        ]
      );
      
    } catch (error) {
      console.error('Error handling ad press:', error);
    }
  };

  if (!currentAd) {
    return null;
  }

  const shimmerTranslateX = shimmerAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [-100, 100],
  });

  return (
    <Animated.View style={[styles.container, {opacity: fadeAnim}]}>
      <TouchableOpacity
        style={styles.adContainer}
        onPress={handleAdPress}
        activeOpacity={0.9}>
        
        <LinearGradient
          colors={currentAd.colors}
          style={styles.gradient}
          start={{x: 0, y: 0}}
          end={{x: 1, y: 1}}>
          
          {/* Shimmer effect */}
          <Animated.View
            style={[
              styles.shimmer,
              {
                transform: [{translateX: shimmerTranslateX}],
              },
            ]}
          />
          
          <View style={styles.content}>
            <View style={styles.textContainer}>
              <Text style={styles.title}>{currentAd.title}</Text>
              <Text style={styles.subtitle}>{currentAd.subtitle}</Text>
            </View>
            
            <View style={styles.ctaContainer}>
              <Text style={styles.ctaText}>{currentAd.cta}</Text>
              <Icon name="arrow-forward" size={18} color="#fff" />
            </View>
          </View>
          
          {/* Ad label */}
          <View style={styles.adLabel}>
            <Text style={styles.adLabelText}>Reklama</Text>
          </View>
        </LinearGradient>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  adContainer: {
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 6,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 3},
    shadowOpacity: 0.2,
    shadowRadius: 6,
  },
  gradient: {
    padding: 20,
    position: 'relative',
    overflow: 'hidden',
  },
  shimmer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    width: 50,
    transform: [{skewX: '-20deg'}],
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    position: 'relative',
    zIndex: 2,
  },
  textContainer: {
    flex: 1,
    marginRight: 16,
  },
  title: {
    fontSize: 16,
    fontWeight: '700',
    color: '#fff',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 13,
    color: 'rgba(255, 255, 255, 0.9)',
    fontWeight: '500',
    lineHeight: 18,
  },
  ctaContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
  },
  ctaText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    marginRight: 6,
  },
  adLabel: {
    position: 'absolute',
    top: 8,
    right: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  adLabelText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: '600',
    opacity: 0.8,
  },
});

export default AdBanner;
