<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌌 3D Universe Simulator - Brutální Vesmírná Simulace</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: #000000;
            color: #ffffff;
            overflow: hidden;
            cursor: crosshair;
        }

        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #canvas3d {
            display: block;
            background: #000000;
        }

        .hud {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 100;
        }

        .control-panel {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(15px);
            padding: 20px;
            border-radius: 15px;
            border: 2px solid rgba(0, 255, 255, 0.5);
            min-width: 300px;
            pointer-events: auto;
            box-shadow: 0 0 30px rgba(0, 255, 255, 0.3);
        }

        .physics-panel {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(15px);
            padding: 20px;
            border-radius: 15px;
            border: 2px solid rgba(255, 107, 107, 0.5);
            min-width: 280px;
            pointer-events: auto;
            box-shadow: 0 0 30px rgba(255, 107, 107, 0.3);
        }

        .tools-panel {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(15px);
            padding: 20px;
            border-radius: 15px;
            border: 2px solid rgba(76, 175, 80, 0.5);
            pointer-events: auto;
            box-shadow: 0 0 30px rgba(76, 175, 80, 0.3);
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 20px;
            margin: 5px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 30px rgba(102, 126, 234, 0.6);
        }

        .btn.active {
            background: linear-gradient(45deg, #ff6b6b 0%, #ee5a24 100%);
            box-shadow: 0 4px 20px rgba(255, 107, 107, 0.4);
        }

        .btn.scenario {
            background: linear-gradient(45deg, #2ed573 0%, #1e90ff 100%);
            font-size: 16px;
            padding: 15px 25px;
        }

        .btn.danger {
            background: linear-gradient(45deg, #ff4757 0%, #c44569 100%);
        }

        .slider-container {
            margin: 15px 0;
        }

        .slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #333;
            outline: none;
            margin: 10px 0;
            cursor: pointer;
        }

        .value-display {
            font-size: 14px;
            color: #00ffff;
            font-weight: bold;
            text-shadow: 0 0 10px #00ffff;
        }

        .physics-value {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            font-size: 13px;
            padding: 5px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 5px;
        }

        .physics-label {
            color: #ccc;
        }

        .physics-number {
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-weight: bold;
            text-shadow: 0 0 5px #00ff00;
        }

        .title {
            font-size: 18px;
            font-weight: bold;
            color: #00ffff;
            text-shadow: 0 0 15px #00ffff;
            margin-bottom: 15px;
            text-align: center;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .subtitle {
            font-size: 14px;
            color: #ff6b6b;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .object-info {
            position: absolute;
            background: rgba(0, 0, 0, 0.95);
            border: 2px solid #00ffff;
            border-radius: 10px;
            padding: 15px;
            font-size: 12px;
            pointer-events: none;
            z-index: 200;
            max-width: 250px;
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
        }

        .crosshair {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 30px;
            height: 30px;
            border: 2px solid #00ffff;
            border-radius: 50%;
            pointer-events: none;
            z-index: 150;
            opacity: 0.7;
            animation: pulse 2s infinite;
        }

        .crosshair::before,
        .crosshair::after {
            content: '';
            position: absolute;
            background: #00ffff;
        }

        .crosshair::before {
            top: 50%;
            left: -10px;
            right: -10px;
            height: 2px;
            transform: translateY(-50%);
        }

        .crosshair::after {
            left: 50%;
            top: -10px;
            bottom: -10px;
            width: 2px;
            transform: translateX(-50%);
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.7; transform: translate(-50%, -50%) scale(1); }
            50% { opacity: 1; transform: translate(-50%, -50%) scale(1.1); }
        }

        .mode-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin: 15px 0;
        }

        .scenario-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .warning {
            color: #ff6b6b;
            font-size: 12px;
            margin: 10px 0;
            padding: 10px;
            background: rgba(255, 107, 107, 0.1);
            border: 1px solid #ff6b6b;
            border-radius: 5px;
            animation: blink 2s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.5; }
        }

        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 24px;
            color: #00ffff;
            text-shadow: 0 0 20px #00ffff;
            z-index: 300;
        }

        .fps-counter {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            padding: 10px 20px;
            border-radius: 20px;
            border: 1px solid #00ffff;
            font-size: 14px;
            color: #00ffff;
            pointer-events: none;
        }

        @media (max-width: 768px) {
            .control-panel, .physics-panel {
                position: relative;
                margin: 10px;
                width: auto;
            }
            
            .tools-panel {
                position: relative;
                margin: 10px;
                flex-direction: column;
            }
            
            .mode-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .scenario-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div id="container">
        <canvas id="canvas3d"></canvas>
        
        <div class="loading" id="loading">
            🌌 Načítání vesmíru...
        </div>
        
        <div class="hud" id="hud" style="display: none;">
            <div class="crosshair"></div>
            
            <div class="fps-counter" id="fps-counter">
                FPS: <span id="fps-display">60</span> | Objekty: <span id="object-count">0</span>
            </div>
            
            <div class="control-panel">
                <div class="title">🎮 Ovládání</div>
                
                <div class="subtitle">Typ objektu</div>
                <div class="mode-grid">
                    <button class="btn active" onclick="setMode('planet')" id="mode-planet">🪐 Planeta</button>
                    <button class="btn" onclick="setMode('star')" id="mode-star">⭐ Hvězda</button>
                    <button class="btn" onclick="setMode('asteroid')" id="mode-asteroid">☄️ Asteroid</button>
                    <button class="btn" onclick="setMode('blackhole')" id="mode-blackhole">🕳️ Černá díra</button>
                    <button class="btn" onclick="setMode('ship')" id="mode-ship">🚀 Loď</button>
                    <button class="btn danger" onclick="setMode('delete')" id="mode-delete">🗑️ Smazat</button>
                </div>
                
                <div class="slider-container">
                    <label>🔋 Hmotnost: <span class="value-display" id="mass-value">1.0e24 kg</span></label>
                    <input type="range" class="slider" id="mass-slider" min="20" max="35" value="24" step="0.1" oninput="updateMass(this.value)">
                </div>
                
                <div class="slider-container">
                    <label>⚡ Rychlost: <span class="value-display" id="velocity-value">0 km/s</span></label>
                    <input type="range" class="slider" id="velocity-slider" min="0" max="100" value="0" step="1" oninput="updateVelocity(this.value)">
                </div>
                
                <div class="slider-container">
                    <label>🎯 Velikost: <span class="value-display" id="size-value">1.0x</span></label>
                    <input type="range" class="slider" id="size-slider" min="0.1" max="5" value="1" step="0.1" oninput="updateSize(this.value)">
                </div>
            </div>

            <div class="physics-panel">
                <div class="title">⚗️ Fyzika</div>
                
                <div class="physics-value">
                    <span class="physics-label">Simulační čas:</span>
                    <span class="physics-number" id="sim-time">0.00 s</span>
                </div>
                <div class="physics-value">
                    <span class="physics-label">Celková hmotnost:</span>
                    <span class="physics-number" id="total-mass">0 kg</span>
                </div>
                <div class="physics-value">
                    <span class="physics-label">Celková energie:</span>
                    <span class="physics-number" id="total-energy">0 J</span>
                </div>
                <div class="physics-value">
                    <span class="physics-label">Gravitační síla:</span>
                    <span class="physics-number" id="gravity-strength">100%</span>
                </div>
                
                <div class="slider-container">
                    <label>⏱️ Rychlost času: <span class="value-display" id="time-scale-value">1x</span></label>
                    <input type="range" class="slider" id="time-scale" min="0.1" max="50" value="1" step="0.1" oninput="updateTimeScale(this.value)">
                </div>
                
                <div class="slider-container">
                    <label>🌍 Gravitace: <span class="value-display" id="gravity-value">100%</span></label>
                    <input type="range" class="slider" id="gravity-slider" min="0" max="300" value="100" step="5" oninput="updateGravity(this.value)">
                </div>
                
                <button class="btn" onclick="togglePhysics()" id="physics-toggle">⏸️ Pauza</button>
            </div>

            <div class="tools-panel">
                <div>
                    <div class="subtitle">🌟 Scénáře</div>
                    <div class="scenario-grid">
                        <button class="btn scenario" onclick="createSolarSystem()">☀️ Sluneční soustava</button>
                        <button class="btn scenario" onclick="createBinarySystem()">⭐⭐ Dvojhvězda</button>
                        <button class="btn scenario" onclick="createGalaxy()">🌌 Galaxie</button>
                        <button class="btn scenario" onclick="createAsteroidField()">☄️ Asteroidové pole</button>
                    </div>
                </div>
                
                <div>
                    <div class="subtitle">🛠️ Nástroje</div>
                    <button class="btn" onclick="toggleTrails()">✨ Stopy</button>
                    <button class="btn" onclick="toggleCollisions()">💥 Kolize</button>
                    <button class="btn" onclick="toggleRelativity()">⚡ Relativita</button>
                    <button class="btn danger" onclick="clearUniverse()">💥 Vymazat vše</button>
                </div>
            </div>
        </div>

        <div class="object-info" id="object-info" style="display: none;">
            <div id="info-content"></div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="universe-3d-engine.js"></script>
</body>
</html>
