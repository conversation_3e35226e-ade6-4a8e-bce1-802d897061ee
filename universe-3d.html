<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌌 3D Universe Simulator - Brutální Vesmírná Simulace</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: #000000;
            color: #ffffff;
            overflow: hidden;
            cursor: crosshair;
        }

        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #canvas3d {
            display: block;
            background: #000000;
        }

        .hud {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 100;
        }

        .control-panel {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(15px);
            padding: 20px;
            border-radius: 15px;
            border: 2px solid rgba(0, 255, 255, 0.5);
            min-width: 300px;
            pointer-events: auto;
            box-shadow: 0 0 30px rgba(0, 255, 255, 0.3);
        }

        .physics-panel {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(15px);
            padding: 20px;
            border-radius: 15px;
            border: 2px solid rgba(255, 107, 107, 0.5);
            min-width: 280px;
            pointer-events: auto;
            box-shadow: 0 0 30px rgba(255, 107, 107, 0.3);
        }

        .tools-panel {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(15px);
            padding: 20px;
            border-radius: 15px;
            border: 2px solid rgba(76, 175, 80, 0.5);
            pointer-events: auto;
            box-shadow: 0 0 30px rgba(76, 175, 80, 0.3);
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 20px;
            margin: 5px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 30px rgba(102, 126, 234, 0.6);
        }

        .btn.active {
            background: linear-gradient(45deg, #ff6b6b 0%, #ee5a24 100%);
            box-shadow: 0 4px 20px rgba(255, 107, 107, 0.4);
        }

        .btn.scenario {
            background: linear-gradient(45deg, #2ed573 0%, #1e90ff 100%);
            font-size: 16px;
            padding: 15px 25px;
        }

        .btn.danger {
            background: linear-gradient(45deg, #ff4757 0%, #c44569 100%);
        }

        .slider-container {
            margin: 15px 0;
        }

        .slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #333;
            outline: none;
            margin: 10px 0;
            cursor: pointer;
        }

        .value-display {
            font-size: 14px;
            color: #00ffff;
            font-weight: bold;
            text-shadow: 0 0 10px #00ffff;
        }

        .physics-value {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            font-size: 13px;
            padding: 5px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 5px;
        }

        .physics-label {
            color: #ccc;
        }

        .physics-number {
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-weight: bold;
            text-shadow: 0 0 5px #00ff00;
        }

        .title {
            font-size: 18px;
            font-weight: bold;
            color: #00ffff;
            text-shadow: 0 0 15px #00ffff;
            margin-bottom: 15px;
            text-align: center;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .subtitle {
            font-size: 14px;
            color: #ff6b6b;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .object-info {
            position: absolute;
            background: rgba(0, 0, 0, 0.95);
            border: 2px solid #00ffff;
            border-radius: 10px;
            padding: 15px;
            font-size: 12px;
            pointer-events: none;
            z-index: 200;
            max-width: 250px;
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
        }

        .crosshair {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 30px;
            height: 30px;
            border: 2px solid #00ffff;
            border-radius: 50%;
            pointer-events: none;
            z-index: 150;
            opacity: 0.7;
            animation: pulse 2s infinite;
        }

        .crosshair::before,
        .crosshair::after {
            content: '';
            position: absolute;
            background: #00ffff;
        }

        .crosshair::before {
            top: 50%;
            left: -10px;
            right: -10px;
            height: 2px;
            transform: translateY(-50%);
        }

        .crosshair::after {
            left: 50%;
            top: -10px;
            bottom: -10px;
            width: 2px;
            transform: translateX(-50%);
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.7; transform: translate(-50%, -50%) scale(1); }
            50% { opacity: 1; transform: translate(-50%, -50%) scale(1.1); }
        }

        .mode-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin: 15px 0;
        }

        .scenario-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .warning {
            color: #ff6b6b;
            font-size: 12px;
            margin: 10px 0;
            padding: 10px;
            background: rgba(255, 107, 107, 0.1);
            border: 1px solid #ff6b6b;
            border-radius: 5px;
            animation: blink 2s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.5; }
        }

        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 24px;
            color: #00ffff;
            text-shadow: 0 0 20px #00ffff;
            z-index: 300;
        }

        .fps-counter {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            padding: 10px 20px;
            border-radius: 20px;
            border: 1px solid #00ffff;
            font-size: 14px;
            color: #00ffff;
            pointer-events: none;
        }

        @media (max-width: 768px) {
            .control-panel, .physics-panel {
                position: relative;
                margin: 10px;
                width: auto;
            }

            .tools-panel {
                position: relative;
                margin: 10px;
                flex-direction: column;
            }

            .mode-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .scenario-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div id="container">
        <canvas id="canvas3d"></canvas>

        <div class="loading" id="loading">
            🌌 Načítání vesmíru...
        </div>

        <div class="hud" id="hud" style="display: none;">
            <div class="crosshair"></div>

            <div class="fps-counter" id="fps-counter">
                FPS: <span id="fps-display">60</span> | Objekty: <span id="object-count">0</span>
            </div>

            <div class="control-panel">
                <div class="title">🎮 Ovládání</div>

                <div class="subtitle">Typ objektu</div>
                <div class="mode-grid">
                    <button class="btn active" onclick="setMode('planet')" id="mode-planet">🪐 Planeta</button>
                    <button class="btn" onclick="setMode('star')" id="mode-star">⭐ Hvězda</button>
                    <button class="btn" onclick="setMode('asteroid')" id="mode-asteroid">☄️ Asteroid</button>
                    <button class="btn" onclick="setMode('blackhole')" id="mode-blackhole">🕳️ Černá díra</button>
                    <button class="btn" onclick="setMode('ship')" id="mode-ship">🚀 Loď</button>
                    <button class="btn danger" onclick="setMode('delete')" id="mode-delete">🗑️ Smazat</button>
                </div>

                <div class="slider-container">
                    <label>🔋 Hmotnost: <span class="value-display" id="mass-value">1.0e24 kg</span></label>
                    <input type="range" class="slider" id="mass-slider" min="20" max="35" value="24" step="0.1" oninput="updateMass(this.value)">
                </div>

                <div class="slider-container">
                    <label>⚡ Rychlost: <span class="value-display" id="velocity-value">0 km/s</span></label>
                    <input type="range" class="slider" id="velocity-slider" min="0" max="100" value="0" step="1" oninput="updateVelocity(this.value)">
                </div>

                <div class="slider-container">
                    <label>🎯 Velikost: <span class="value-display" id="size-value">1.0x</span></label>
                    <input type="range" class="slider" id="size-slider" min="0.1" max="5" value="1" step="0.1" oninput="updateSize(this.value)">
                </div>
            </div>

            <div class="physics-panel">
                <div class="title">⚗️ Fyzika</div>

                <div class="physics-value">
                    <span class="physics-label">Simulační čas:</span>
                    <span class="physics-number" id="sim-time">0.00 s</span>
                </div>
                <div class="physics-value">
                    <span class="physics-label">Celková hmotnost:</span>
                    <span class="physics-number" id="total-mass">0 kg</span>
                </div>
                <div class="physics-value">
                    <span class="physics-label">Celková energie:</span>
                    <span class="physics-number" id="total-energy">0 J</span>
                </div>
                <div class="physics-value">
                    <span class="physics-label">Gravitační síla:</span>
                    <span class="physics-number" id="gravity-strength">100%</span>
                </div>

                <div class="slider-container">
                    <label>⏱️ Rychlost času: <span class="value-display" id="time-scale-value">1x</span></label>
                    <input type="range" class="slider" id="time-scale" min="0.1" max="50" value="1" step="0.1" oninput="updateTimeScale(this.value)">
                </div>

                <div class="slider-container">
                    <label>🌍 Gravitace: <span class="value-display" id="gravity-value">100%</span></label>
                    <input type="range" class="slider" id="gravity-slider" min="0" max="300" value="100" step="5" oninput="updateGravity(this.value)">
                </div>

                <button class="btn" onclick="togglePhysics()" id="physics-toggle">⏸️ Pauza</button>
            </div>

            <div class="tools-panel">
                <div>
                    <div class="subtitle">🌟 Scénáře</div>
                    <div class="scenario-grid">
                        <button class="btn scenario" onclick="createSolarSystem()">☀️ Sluneční soustava</button>
                        <button class="btn scenario" onclick="createBinarySystem()">⭐⭐ Dvojhvězda</button>
                        <button class="btn scenario" onclick="createGalaxy()">🌌 Galaxie</button>
                        <button class="btn scenario" onclick="createAsteroidField()">☄️ Asteroidové pole</button>
                    </div>
                </div>

                <div>
                    <div class="subtitle">🛠️ Nástroje</div>
                    <button class="btn" onclick="toggleTrails()">✨ Stopy</button>
                    <button class="btn" onclick="toggleCollisions()">💥 Kolize</button>
                    <button class="btn" onclick="toggleRelativity()">⚡ Relativita</button>
                    <button class="btn danger" onclick="clearUniverse()">💥 Vymazat vše</button>
                </div>
            </div>
        </div>

        <div class="object-info" id="object-info" style="display: none;">
            <div id="info-content"></div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script>
        // OrbitControls přímo v kódu pro rychlé načtení
        THREE.OrbitControls = function ( object, domElement ) {
            this.object = object;
            this.domElement = ( domElement !== undefined ) ? domElement : document;
            this.enabled = true;
            this.target = new THREE.Vector3();
            this.minDistance = 0;
            this.maxDistance = Infinity;
            this.minZoom = 0;
            this.maxZoom = Infinity;
            this.minPolarAngle = 0;
            this.maxPolarAngle = Math.PI;
            this.minAzimuthAngle = - Infinity;
            this.maxAzimuthAngle = Infinity;
            this.enableDamping = false;
            this.dampingFactor = 0.25;
            this.enableZoom = true;
            this.zoomSpeed = 1.0;
            this.enableRotate = true;
            this.rotateSpeed = 1.0;
            this.enablePan = true;
            this.panSpeed = 1.0;
            this.screenSpacePanning = false;
            this.keyPanSpeed = 7.0;
            this.autoRotate = false;
            this.autoRotateSpeed = 2.0;
            this.enableKeys = true;
            this.keys = { LEFT: 37, UP: 38, RIGHT: 39, BOTTOM: 40 };
            this.mouseButtons = { LEFT: THREE.MOUSE.LEFT, MIDDLE: THREE.MOUSE.MIDDLE, RIGHT: THREE.MOUSE.RIGHT };
            this.target0 = this.target.clone();
            this.position0 = this.object.position.clone();
            this.zoom0 = this.object.zoom;

            var scope = this;
            var changeEvent = { type: 'change' };
            var startEvent = { type: 'start' };
            var endEvent = { type: 'end' };
            var STATE = { NONE: - 1, ROTATE: 0, DOLLY: 1, PAN: 2, TOUCH_ROTATE: 3, TOUCH_DOLLY_PAN: 4 };
            var state = STATE.NONE;
            var EPS = 0.000001;
            var spherical = new THREE.Spherical();
            var sphericalDelta = new THREE.Spherical();
            var scale = 1;
            var panOffset = new THREE.Vector3();
            var zoomChanged = false;
            var rotateStart = new THREE.Vector2();
            var rotateEnd = new THREE.Vector2();
            var rotateDelta = new THREE.Vector2();
            var panStart = new THREE.Vector2();
            var panEnd = new THREE.Vector2();
            var panDelta = new THREE.Vector2();
            var dollyStart = new THREE.Vector2();
            var dollyEnd = new THREE.Vector2();
            var dollyDelta = new THREE.Vector2();

            this.update = function () {
                var offset = new THREE.Vector3();
                var quat = new THREE.Quaternion().setFromUnitVectors( object.up, new THREE.Vector3( 0, 1, 0 ) );
                var quatInverse = quat.clone().inverse();
                var lastPosition = new THREE.Vector3();
                var lastQuaternion = new THREE.Quaternion();

                return function update() {
                    var position = scope.object.position;
                    offset.copy( position ).sub( scope.target );
                    offset.applyQuaternion( quat );
                    spherical.setFromVector3( offset );
                    if ( scope.autoRotate && state === STATE.NONE ) {
                        rotateLeft( getAutoRotationAngle() );
                    }
                    spherical.theta += sphericalDelta.theta;
                    spherical.phi += sphericalDelta.phi;
                    spherical.theta = Math.max( scope.minAzimuthAngle, Math.min( scope.maxAzimuthAngle, spherical.theta ) );
                    spherical.phi = Math.max( scope.minPolarAngle, Math.min( scope.maxPolarAngle, spherical.phi ) );
                    spherical.makeSafe();
                    spherical.radius *= scale;
                    spherical.radius = Math.max( scope.minDistance, Math.min( scope.maxDistance, spherical.radius ) );
                    scope.target.add( panOffset );
                    offset.setFromSpherical( spherical );
                    offset.applyQuaternion( quatInverse );
                    position.copy( scope.target ).add( offset );
                    scope.object.lookAt( scope.target );
                    if ( scope.enableDamping === true ) {
                        sphericalDelta.theta *= ( 1 - scope.dampingFactor );
                        sphericalDelta.phi *= ( 1 - scope.dampingFactor );
                        panOffset.multiplyScalar( 1 - scope.dampingFactor );
                    } else {
                        sphericalDelta.set( 0, 0, 0 );
                        panOffset.set( 0, 0, 0 );
                    }
                    scale = 1;
                    if ( zoomChanged || lastPosition.distanceToSquared( scope.object.position ) > EPS || 8 * ( 1 - lastQuaternion.dot( scope.object.quaternion ) ) > EPS ) {
                        scope.dispatchEvent( changeEvent );
                        lastPosition.copy( scope.object.position );
                        lastQuaternion.copy( scope.object.quaternion );
                        zoomChanged = false;
                        return true;
                    }
                    return false;
                };
            }();

            this.reset = function () {
                scope.target.copy( scope.target0 );
                scope.object.position.copy( scope.position0 );
                scope.object.zoom = scope.zoom0;
                scope.object.updateProjectionMatrix();
                scope.dispatchEvent( changeEvent );
                scope.update();
                state = STATE.NONE;
            };

            function getAutoRotationAngle() {
                return 2 * Math.PI / 60 / 60 * scope.autoRotateSpeed;
            }

            function getZoomScale() {
                return Math.pow( 0.95, scope.zoomSpeed );
            }

            function rotateLeft( angle ) {
                sphericalDelta.theta -= angle;
            }

            function rotateUp( angle ) {
                sphericalDelta.phi -= angle;
            }

            var panLeft = function () {
                var v = new THREE.Vector3();
                return function panLeft( distance, objectMatrix ) {
                    v.setFromMatrixColumn( objectMatrix, 0 );
                    v.multiplyScalar( - distance );
                    panOffset.add( v );
                };
            }();

            var panUp = function () {
                var v = new THREE.Vector3();
                return function panUp( distance, objectMatrix ) {
                    if ( scope.screenSpacePanning === true ) {
                        v.setFromMatrixColumn( objectMatrix, 1 );
                    } else {
                        v.setFromMatrixColumn( objectMatrix, 0 );
                        v.crossVectors( scope.object.up, v );
                    }
                    v.multiplyScalar( distance );
                    panOffset.add( v );
                };
            }();

            var pan = function () {
                var offset = new THREE.Vector3();
                return function pan( deltaX, deltaY ) {
                    var element = scope.domElement === document ? scope.domElement.body : scope.domElement;
                    if ( scope.object.isPerspectiveCamera ) {
                        var position = scope.object.position;
                        offset.copy( position ).sub( scope.target );
                        var targetDistance = offset.length();
                        targetDistance *= Math.tan( ( scope.object.fov / 2 ) * Math.PI / 180.0 );
                        panLeft( 2 * deltaX * targetDistance / element.clientHeight, scope.object.matrix );
                        panUp( 2 * deltaY * targetDistance / element.clientHeight, scope.object.matrix );
                    } else if ( scope.object.isOrthographicCamera ) {
                        panLeft( deltaX * ( scope.object.right - scope.object.left ) / scope.object.zoom / element.clientWidth, scope.object.matrix );
                        panUp( deltaY * ( scope.object.top - scope.object.bottom ) / scope.object.zoom / element.clientHeight, scope.object.matrix );
                    } else {
                        console.warn( 'WARNING: OrbitControls.js encountered an unknown camera type - pan disabled.' );
                        scope.enablePan = false;
                    }
                };
            }();

            function dollyIn( dollyScale ) {
                if ( scope.object.isPerspectiveCamera ) {
                    scale /= dollyScale;
                } else if ( scope.object.isOrthographicCamera ) {
                    scope.object.zoom = Math.max( scope.minZoom, Math.min( scope.maxZoom, scope.object.zoom * dollyScale ) );
                    scope.object.updateProjectionMatrix();
                    zoomChanged = true;
                } else {
                    console.warn( 'WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled.' );
                    scope.enableZoom = false;
                }
            }

            function dollyOut( dollyScale ) {
                if ( scope.object.isPerspectiveCamera ) {
                    scale *= dollyScale;
                } else if ( scope.object.isOrthographicCamera ) {
                    scope.object.zoom = Math.max( scope.minZoom, Math.min( scope.maxZoom, scope.object.zoom / dollyScale ) );
                    scope.object.updateProjectionMatrix();
                    zoomChanged = true;
                } else {
                    console.warn( 'WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled.' );
                    scope.enableZoom = false;
                }
            }

            function handleMouseDownRotate( event ) {
                rotateStart.set( event.clientX, event.clientY );
            }

            function handleMouseDownDolly( event ) {
                dollyStart.set( event.clientX, event.clientY );
            }

            function handleMouseDownPan( event ) {
                panStart.set( event.clientX, event.clientY );
            }

            function handleMouseMoveRotate( event ) {
                rotateEnd.set( event.clientX, event.clientY );
                rotateDelta.subVectors( rotateEnd, rotateStart ).multiplyScalar( scope.rotateSpeed );
                var element = scope.domElement === document ? scope.domElement.body : scope.domElement;
                rotateLeft( 2 * Math.PI * rotateDelta.x / element.clientHeight );
                rotateUp( 2 * Math.PI * rotateDelta.y / element.clientHeight );
                rotateStart.copy( rotateEnd );
                scope.update();
            }

            function handleMouseMoveDolly( event ) {
                dollyEnd.set( event.clientX, event.clientY );
                dollyDelta.subVectors( dollyEnd, dollyStart );
                if ( dollyDelta.y > 0 ) {
                    dollyIn( getZoomScale() );
                } else if ( dollyDelta.y < 0 ) {
                    dollyOut( getZoomScale() );
                }
                dollyStart.copy( dollyEnd );
                scope.update();
            }

            function handleMouseMovePan( event ) {
                panEnd.set( event.clientX, event.clientY );
                panDelta.subVectors( panEnd, panStart ).multiplyScalar( scope.panSpeed );
                pan( panDelta.x, panDelta.y );
                panStart.copy( panEnd );
                scope.update();
            }

            function handleMouseWheel( event ) {
                if ( event.deltaY < 0 ) {
                    dollyOut( getZoomScale() );
                } else if ( event.deltaY > 0 ) {
                    dollyIn( getZoomScale() );
                }
                scope.update();
            }

            function onMouseDown( event ) {
                if ( scope.enabled === false ) return;
                event.preventDefault();
                switch ( event.button ) {
                    case scope.mouseButtons.LEFT:
                        if ( event.ctrlKey || event.metaKey || event.shiftKey ) {
                            if ( scope.enablePan === false ) return;
                            handleMouseDownPan( event );
                            state = STATE.PAN;
                        } else {
                            if ( scope.enableRotate === false ) return;
                            handleMouseDownRotate( event );
                            state = STATE.ROTATE;
                        }
                        break;
                    case scope.mouseButtons.MIDDLE:
                        if ( scope.enableZoom === false ) return;
                        handleMouseDownDolly( event );
                        state = STATE.DOLLY;
                        break;
                    case scope.mouseButtons.RIGHT:
                        if ( scope.enablePan === false ) return;
                        handleMouseDownPan( event );
                        state = STATE.PAN;
                        break;
                }
                if ( state !== STATE.NONE ) {
                    document.addEventListener( 'mousemove', onMouseMove, false );
                    document.addEventListener( 'mouseup', onMouseUp, false );
                    scope.dispatchEvent( startEvent );
                }
            }

            function onMouseMove( event ) {
                if ( scope.enabled === false ) return;
                event.preventDefault();
                switch ( state ) {
                    case STATE.ROTATE:
                        if ( scope.enableRotate === false ) return;
                        handleMouseMoveRotate( event );
                        break;
                    case STATE.DOLLY:
                        if ( scope.enableZoom === false ) return;
                        handleMouseMoveDolly( event );
                        break;
                    case STATE.PAN:
                        if ( scope.enablePan === false ) return;
                        handleMouseMovePan( event );
                        break;
                }
            }

            function onMouseUp( event ) {
                if ( scope.enabled === false ) return;
                document.removeEventListener( 'mousemove', onMouseMove, false );
                document.removeEventListener( 'mouseup', onMouseUp, false );
                scope.dispatchEvent( endEvent );
                state = STATE.NONE;
            }

            function onMouseWheel( event ) {
                if ( scope.enabled === false || scope.enableZoom === false || ( state !== STATE.NONE && state !== STATE.ROTATE ) ) return;
                event.preventDefault();
                event.stopPropagation();
                scope.dispatchEvent( startEvent );
                handleMouseWheel( event );
                scope.dispatchEvent( endEvent );
            }

            scope.domElement.addEventListener( 'contextmenu', function ( event ) { event.preventDefault(); }, false );
            scope.domElement.addEventListener( 'mousedown', onMouseDown, false );
            scope.domElement.addEventListener( 'wheel', onMouseWheel, false );

            scope.update();
        };

        THREE.OrbitControls.prototype = Object.create( THREE.EventDispatcher.prototype );
        THREE.OrbitControls.prototype.constructor = THREE.OrbitControls;
    </script>
    <script src="universe-3d-engine.js"></script>
</body>
</html>
