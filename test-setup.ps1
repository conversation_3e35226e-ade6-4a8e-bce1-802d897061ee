# MindFlow - Quick Test Setup Script (PowerShell)
# Rychlé nastavení pro testování aplikace

Write-Host "🧠 MindFlow - Quick Test Setup" -ForegroundColor Cyan
Write-Host "==============================" -ForegroundColor Cyan

function Write-Info {
    param($Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param($Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param($Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param($Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Check if we're in the right directory
if (-not (Test-Path "package.json")) {
    Write-Error "package.json not found. Please run this script from the project root."
    exit 1
}

Write-Info "Setting up MindFlow for testing..."

# 1. Install dependencies
Write-Info "Installing dependencies..."
if (Get-Command yarn -ErrorAction SilentlyContinue) {
    yarn install
} else {
    npm install
}

# 2. Create test data directory
New-Item -ItemType Directory -Force -Path "test-data" | Out-Null

# 3. Create mock data for testing
Write-Info "Creating test data..."

$mockData = @"
{
  "testUsers": [
    {
      "id": "test_user_1",
      "name": "Anna Testová",
      "email": "<EMAIL>",
      "joinDate": "2024-01-01",
      "streakDays": 15,
      "totalMeditations": 45,
      "averageMood": 7.2,
      "isPremium": false
    },
    {
      "id": "test_user_2", 
      "name": "Petr Zkušební",
      "email": "<EMAIL>",
      "joinDate": "2024-01-15",
      "streakDays": 7,
      "totalMeditations": 12,
      "averageMood": 6.8,
      "isPremium": true
    }
  ],
  "moodHistory": [
    {"date": "2024-01-20", "mood": "good", "timestamp": 1705747200000},
    {"date": "2024-01-21", "mood": "amazing", "timestamp": 1705833600000},
    {"date": "2024-01-22", "mood": "okay", "timestamp": 1705920000000},
    {"date": "2024-01-23", "mood": "good", "timestamp": 1706006400000},
    {"date": "2024-01-24", "mood": "bad", "timestamp": 1706092800000}
  ]
}
"@

$mockData | Out-File -FilePath "test-data/mock-users.json" -Encoding UTF8

# 4. Create test configuration
$testConfig = @"
// MindFlow Test Configuration
export const TEST_CONFIG = {
  // Enable test mode
  TEST_MODE: true,
  
  // Mock data
  USE_MOCK_DATA: true,
  
  // Skip real API calls
  SKIP_ANALYTICS: true,
  SKIP_ADS: false, // Keep ads for testing monetization
  
  // Test user
  TEST_USER: {
    id: 'test_user_demo',
    name: 'Demo Uživatel',
    email: '<EMAIL>',
    isPremium: false,
    streakDays: 3,
    totalMeditations: 8
  },
  
  // Test ads (using AdMob test IDs)
  TEST_AD_UNITS: {
    android: {
      banner: 'ca-app-pub-3940256099942544/6300978111',
      interstitial: 'ca-app-pub-3940256099942544/1033173712',
      rewarded: 'ca-app-pub-3940256099942544/5224354917'
    },
    ios: {
      banner: 'ca-app-pub-3940256099942544/2934735716',
      interstitial: 'ca-app-pub-3940256099942544/4411468910', 
      rewarded: 'ca-app-pub-3940256099942544/1712485313'
    }
  },
  
  // Accelerated timers for testing
  FAST_MODE: {
    notificationInterval: 10000, // 10 seconds instead of hours
    adRotationInterval: 5000,    // 5 seconds instead of minutes
    analyticsSync: 30000         // 30 seconds instead of 5 minutes
  }
};
"@

$testConfig | Out-File -FilePath "test-config.js" -Encoding UTF8

# 5. Create demo launcher
$demoScript = @"
@echo off
echo 🧠 MindFlow Demo Launcher
echo ========================
echo.
echo 🎯 Demo Features:
echo • Mood tracking with 5 different moods
echo • Guided meditation sessions
echo • Smart ad integration (test ads)
echo • Progress analytics
echo • Premium features preview
echo • Push notifications
echo.
echo 📱 Starting Android app...
echo    (Make sure you have Android emulator running)
echo.

start /B npm start
timeout /t 5 /nobreak >nul
npm run android
"@

$demoScript | Out-File -FilePath "demo.bat" -Encoding ASCII

# 6. Create test checklist
$testChecklist = @"
# 🧠 MindFlow - Test Checklist

## 📱 **Quick Start**
``````
./demo.bat  # Spustí demo aplikaci (Windows)
``````

## ✅ **Test Scenarios**

### **1. Mood Tracking Test**
- [ ] Otevři aplikaci
- [ ] Klikni na různé nálady (🤩😊😐😔😢)
- [ ] Zkontroluj, že se nálada uloží
- [ ] Ověř analytics v konzoli
- [ ] Zkontroluj streak counter

### **2. Ad Integration Test**
- [ ] Zkontroluj banner reklamu nahoře
- [ ] Počkej 5 sekund na rotaci reklamy
- [ ] Klikni na reklamu (test ad - bezpečné)
- [ ] Ověř revenue tracking v konzoli
- [ ] Zkontroluj různé typy reklam

### **3. Navigation Test**
- [ ] Přepni mezi všemi 5 taby
- [ ] Zkontroluj smooth animace
- [ ] Ověř, že se obsah načítá
- [ ] Test zpět tlačítka

### **4. Meditation Test**
- [ ] Jdi na Meditation tab
- [ ] Spusť quick meditation
- [ ] Test pause/resume (pokud implementováno)
- [ ] Zkontroluj progress update

### **5. Premium Features Test**
- [ ] Zkus přístup k premium funkci
- [ ] Ověř upgrade prompt
- [ ] Test subscription flow
- [ ] Zkontroluj premium badge

## 💡 **Test Tips**
- Všechny reklamy jsou testovací (bezpečné klikat)
- Analytics se logují do konzole
- Fast mode = rychlejší timers pro testování
- Mock data jsou přednastavená
- Test user: <EMAIL>

## 🎯 **Success Criteria**
- [ ] Aplikace se spustí bez crashů
- [ ] Všechny funkce fungují
- [ ] Reklamy se zobrazují a rotují
- [ ] Analytics trackují eventy
- [ ] Data se ukládají správně
- [ ] UI je responsive
- [ ] Performance je dobrá (>30 FPS)
"@

$testChecklist | Out-File -FilePath "TEST_CHECKLIST.md" -Encoding UTF8

# 7. Update package.json with test scripts
Write-Info "Adding test scripts to package.json..."

# Read package.json
$packageJson = Get-Content "package.json" | ConvertFrom-Json

# Add test scripts
if (-not $packageJson.scripts) {
    $packageJson | Add-Member -Type NoteProperty -Name "scripts" -Value @{}
}

$packageJson.scripts | Add-Member -Type NoteProperty -Name "test:setup" -Value "node run-tests.js" -Force
$packageJson.scripts | Add-Member -Type NoteProperty -Name "test:android" -Value "npm run android -- --mode=test" -Force
$packageJson.scripts | Add-Member -Type NoteProperty -Name "test:ios" -Value "npm run ios -- --mode=test" -Force
$packageJson.scripts | Add-Member -Type NoteProperty -Name "demo" -Value "echo `"🧠 Starting MindFlow Demo...`" && npm run android" -Force

# Save package.json
$packageJson | ConvertTo-Json -Depth 10 | Out-File "package.json" -Encoding UTF8

Write-Success "Test setup completed!"

Write-Host ""
Write-Host "🎉 MindFlow Test Environment Ready!" -ForegroundColor Green
Write-Host "==================================" -ForegroundColor Green
Write-Host ""
Write-Host "🚀 Quick Start:" -ForegroundColor Cyan
Write-Host "   ./demo.bat                   # Start demo app (Windows)" -ForegroundColor White
Write-Host "   npm run android              # Start Android app" -ForegroundColor White
Write-Host ""
Write-Host "📋 Test Files Created:" -ForegroundColor Cyan
Write-Host "   • test-data/mock-users.json  # Test data" -ForegroundColor White
Write-Host "   • test-config.js             # Test configuration" -ForegroundColor White
Write-Host "   • demo.bat                   # Demo launcher (Windows)" -ForegroundColor White
Write-Host "   • TEST_CHECKLIST.md          # Test checklist" -ForegroundColor White
Write-Host ""
Write-Host "💡 Test Features:" -ForegroundColor Cyan
Write-Host "   ✅ Mock user data preloaded" -ForegroundColor Green
Write-Host "   ✅ Test ads (safe to click)" -ForegroundColor Green
Write-Host "   ✅ Analytics logging" -ForegroundColor Green
Write-Host "   ✅ Fast mode timers" -ForegroundColor Green
Write-Host "   ✅ Demo scenarios" -ForegroundColor Green
Write-Host ""
Write-Host "🎯 Next Steps:" -ForegroundColor Cyan
Write-Host "   1. Run: ./demo.bat" -ForegroundColor White
Write-Host "   2. Follow TEST_CHECKLIST.md" -ForegroundColor White
Write-Host "   3. Test all features" -ForegroundColor White
Write-Host "   4. Report any issues" -ForegroundColor White
Write-Host ""
Write-Host "Happy testing! 🧠✨" -ForegroundColor Magenta
