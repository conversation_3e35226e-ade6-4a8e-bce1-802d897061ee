import React, { useState } from 'react'
import './StrategyPanel.css'

const StrategyPanel = ({ onApplyStrategy, currentBet, disabled }) => {
  const [selectedStrategy, setSelectedStrategy] = useState('')

  const strategies = {
    martingale: {
      name: '<PERSON><PERSON><PERSON>',
      description: '<PERSON><PERSON>z<PERSON> na červené, při prohře zdvojnásobení',
      bets: [
        { type: 'red', amount: currentBet }
      ]
    },
    fi<PERSON><PERSON><PERSON>: {
      name: '<PERSON><PERSON><PERSON><PERSON>',
      description: 'Sázky podle Fibonacciho p<PERSON>loupnosti',
      bets: [
        { type: 'black', amount: currentBet },
        { type: 'even', amount: Math.floor(currentBet * 0.6) }
      ]
    },
    dalembert: {
      name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      description: 'Konzervativní progrese na sudé/liché',
      bets: [
        { type: 'even', amount: currentBet },
        { type: 'low', amount: Math.floor(currentBet * 0.5) }
      ]
    },
    hotNumbers: {
      name: 'Hot Numbers',
      description: '<PERSON><PERSON><PERSON><PERSON> na "horká" čísla + červené',
      bets: [
        { type: 'number-7', amount: Math.floor(currentBet * 0.3) },
        { type: 'number-17', amount: Math.floor(currentBet * 0.3) },
        { type: 'number-23', amount: Math.floor(currentBet * 0.3) },
        { type: 'red', amount: currentBet }
      ]
    },
    corners: {
      name: 'Rohy & Tucty',
      description: 'Kombinace rohových sázek a tuctů',
      bets: [
        { type: 'dozen2', amount: currentBet },
        { type: 'column1', amount: Math.floor(currentBet * 0.7) },
        { type: 'black', amount: Math.floor(currentBet * 0.5) }
      ]
    },
    redBlackSplit: {
      name: 'Červená/Černá Split',
      description: 'Rozložení rizika mezi barvy',
      bets: [
        { type: 'red', amount: Math.floor(currentBet * 0.6) },
        { type: 'black', amount: Math.floor(currentBet * 0.4) },
        { type: 'number-0', amount: Math.floor(currentBet * 0.2) }
      ]
    }
  }

  const handleApplyStrategy = () => {
    if (selectedStrategy && strategies[selectedStrategy]) {
      onApplyStrategy(strategies[selectedStrategy].bets)
    }
  }

  const getTotalBetAmount = (strategyKey) => {
    return strategies[strategyKey].bets.reduce((sum, bet) => sum + bet.amount, 0)
  }

  return (
    <div className="strategy-panel">
      <h3>🎯 Herní Strategie</h3>
      <p className="strategy-description">
        Vyberte strategii a automaticky se nastaví sázky podle osvědčených systémů.
      </p>

      <div className="strategy-list">
        {Object.entries(strategies).map(([key, strategy]) => (
          <div 
            key={key}
            className={`strategy-item ${selectedStrategy === key ? 'selected' : ''}`}
            onClick={() => setSelectedStrategy(key)}
          >
            <div className="strategy-header">
              <h4>{strategy.name}</h4>
              <span className="strategy-cost">
                {getTotalBetAmount(key)} Kč
              </span>
            </div>
            <p className="strategy-desc">{strategy.description}</p>
            
            <div className="strategy-bets">
              {strategy.bets.map((bet, index) => (
                <span key={index} className="bet-preview">
                  {bet.type.replace('number-', '').replace('dozen', 'tucet').replace('column', 'sloupec')}: {bet.amount}Kč
                </span>
              ))}
            </div>
          </div>
        ))}
      </div>

      <div className="strategy-controls">
        <button 
          className="btn btn-strategy"
          onClick={handleApplyStrategy}
          disabled={disabled || !selectedStrategy}
        >
          {selectedStrategy ? `Použít ${strategies[selectedStrategy]?.name}` : 'Vyberte strategii'}
        </button>
        
        {selectedStrategy && (
          <div className="selected-strategy-info">
            <strong>Celková sázka: {getTotalBetAmount(selectedStrategy)} Kč</strong>
          </div>
        )}
      </div>

      <div className="strategy-tips">
        <h4>💡 Tipy pro strategie:</h4>
        <ul>
          <li><strong>Martingale:</strong> Vysoké riziko, rychlé zisky nebo ztráty</li>
          <li><strong>Fibonacci:</strong> Střední riziko, postupný růst</li>
          <li><strong>D'Alembert:</strong> Nízké riziko, konzervativní přístup</li>
          <li><strong>Hot Numbers:</strong> Vysoké výplaty, nízká pravděpodobnost</li>
        </ul>
      </div>
    </div>
  )
}

export default StrategyPanel
