import AsyncStorage from '@react-native-async-storage/async-storage';
import AnalyticsService from './AnalyticsService';

class AdService {
  constructor() {
    this.adRevenue = 0;
    this.adClicks = 0;
    this.adImpressions = 0;
    this.initialized = false;
  }

  async initialize() {
    try {
      // Načti ul<PERSON> data o reklamách
      const savedData = await AsyncStorage.getItem('ad_service_data');
      if (savedData) {
        const data = JSON.parse(savedData);
        this.adRevenue = data.revenue || 0;
        this.adClicks = data.clicks || 0;
        this.adImpressions = data.impressions || 0;
      }
      
      this.initialized = true;
      console.log('📢 AdService initialized');
      
      // Simulace real-time ad bidding
      this.startAdBidding();
      
    } catch (error) {
      console.error('Error initializing AdService:', error);
    }
  }

  async getPersonalizedAd() {
    try {
      // Získej už<PERSON>á data pro personalizaci
      const userData = await AsyncStorage.getItem('user_data');
      const moodHistory = await AsyncStorage.getItem('mindflow_mood_history');
      
      let userProfile = {};
      if (userData) {
        userProfile = JSON.parse(userData);
      }
      
      let recentMoods = [];
      if (moodHistory) {
        recentMoods = JSON.parse(moodHistory).slice(-7); // Posledních 7 dní
      }
      
      // Personalizované reklamy na základě nálady a chování
      const personalizedAds = this.getAdsForProfile(userProfile, recentMoods);
      
      // Vybere nejrelevantnější reklamu
      const selectedAd = this.selectBestAd(personalizedAds);
      
      // Track impression
      await this.trackAdImpression(selectedAd);
      
      return selectedAd;
      
    } catch (error) {
      console.error('Error getting personalized ad:', error);
      return null;
    }
  }

  getAdsForProfile(userProfile, recentMoods) {
    const allAds = [
      {
        id: 'spotify_premium',
        title: '🎧 Spotify Premium',
        subtitle: '3 měsíce zdarma pro nové uživatele!',
        colors: ['#1DB954', '#1ed760'],
        url: 'https://spotify.com/premium',
        category: 'music',
        cta: 'Získat zdarma',
        targetMoods: ['good', 'amazing'],
        cpm: 12.50, // Cost per mille (za 1000 zobrazení)
        cpc: 0.85,  // Cost per click
        relevanceScore: 0.8,
      },
      {
        id: 'headspace_trial',
        title: '🧘‍♀️ Headspace',
        subtitle: 'Meditace pro lepší spánek a klid',
        colors: ['#FF6B35', '#F7931E'],
        url: 'https://headspace.com',
        category: 'wellness',
        cta: 'Vyzkoušet',
        targetMoods: ['bad', 'terrible', 'okay'],
        cpm: 15.00,
        cpc: 1.20,
        relevanceScore: 0.9,
      },
      {
        id: 'audible_books',
        title: '📚 Audible',
        subtitle: 'Knihy o mindfulness a osobním rozvoji',
        colors: ['#FF9500', '#FFAD33'],
        url: 'https://audible.com',
        category: 'education',
        cta: 'Poslouchat',
        targetMoods: ['good', 'amazing', 'okay'],
        cpm: 10.00,
        cpc: 0.75,
        relevanceScore: 0.7,
      },
      {
        id: 'emma_mattress',
        title: '🛏️ Emma matrace',
        subtitle: 'Lepší spánek = lepší mentální zdraví',
        colors: ['#00C9A7', '#00E5C3'],
        url: 'https://emma-sleep.com',
        category: 'sleep',
        cta: 'Koupit',
        targetMoods: ['bad', 'terrible'],
        cpm: 20.00,
        cpc: 2.50,
        relevanceScore: 0.6,
      },
      {
        id: 'calm_app',
        title: '🌙 Calm',
        subtitle: 'Spánkové příběhy a relaxační hudba',
        colors: ['#2F80ED', '#56CCF2'],
        url: 'https://calm.com',
        category: 'wellness',
        cta: 'Stáhnout',
        targetMoods: ['bad', 'terrible', 'okay'],
        cpm: 14.00,
        cpc: 1.10,
        relevanceScore: 0.85,
      },
      {
        id: 'nike_training',
        title: '💪 Nike Training',
        subtitle: 'Cvičení pro lepší náladu a energii',
        colors: ['#FF6B00', '#FF8500'],
        url: 'https://nike.com/training',
        category: 'fitness',
        cta: 'Začít',
        targetMoods: ['good', 'amazing'],
        cpm: 8.00,
        cpc: 0.60,
        relevanceScore: 0.6,
      },
      {
        id: 'blinkist_books',
        title: '📖 Blinkist',
        subtitle: 'Shrnutí knih za 15 minut',
        colors: ['#00D4AA', '#00E5C3'],
        url: 'https://blinkist.com',
        category: 'education',
        cta: 'Číst',
        targetMoods: ['good', 'amazing', 'okay'],
        cpm: 11.00,
        cpc: 0.90,
        relevanceScore: 0.75,
      },
    ];

    // Filtruj reklamy podle nálady uživatele
    const averageMood = this.calculateAverageMood(recentMoods);
    
    return allAds.filter(ad => {
      // Pokud má reklama target moods, zkontroluj shodu
      if (ad.targetMoods && ad.targetMoods.length > 0) {
        return ad.targetMoods.includes(averageMood);
      }
      return true;
    }).sort((a, b) => {
      // Seřaď podle relevance a CPM (vyšší CPM = více peněz)
      return (b.relevanceScore * b.cpm) - (a.relevanceScore * a.cpm);
    });
  }

  calculateAverageMood(recentMoods) {
    if (!recentMoods || recentMoods.length === 0) {
      return 'okay'; // Default
    }

    const moodValues = {
      amazing: 5,
      good: 4,
      okay: 3,
      bad: 2,
      terrible: 1,
    };

    const total = recentMoods.reduce((sum, moodEntry) => {
      return sum + (moodValues[moodEntry.mood] || 3);
    }, 0);

    const average = total / recentMoods.length;

    // Převeď zpět na mood string
    if (average >= 4.5) return 'amazing';
    if (average >= 3.5) return 'good';
    if (average >= 2.5) return 'okay';
    if (average >= 1.5) return 'bad';
    return 'terrible';
  }

  selectBestAd(ads) {
    if (!ads || ads.length === 0) {
      return null;
    }

    // Weighted random selection based on CPM and relevance
    const weights = ads.map(ad => ad.cpm * ad.relevanceScore);
    const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
    
    let random = Math.random() * totalWeight;
    
    for (let i = 0; i < ads.length; i++) {
      random -= weights[i];
      if (random <= 0) {
        return ads[i];
      }
    }
    
    return ads[0]; // Fallback
  }

  async trackAdImpression(ad) {
    if (!ad) return;

    try {
      this.adImpressions++;
      
      // Simulace revenue z impression (CPM)
      const impressionRevenue = ad.cpm / 1000; // CPM je za 1000 zobrazení
      this.adRevenue += impressionRevenue;
      
      // Analytics
      await AnalyticsService.trackEvent('ad_impression', {
        ad_id: ad.id,
        category: ad.category,
        cpm: ad.cpm,
        revenue: impressionRevenue,
      });
      
      // Uložení dat
      await this.saveAdData();
      
      console.log(`💰 Ad impression: ${ad.id}, Revenue: $${impressionRevenue.toFixed(4)}`);
      
    } catch (error) {
      console.error('Error tracking ad impression:', error);
    }
  }

  async trackAdClick(ad) {
    if (!ad) return;

    try {
      this.adClicks++;
      
      // Simulace revenue z click (CPC)
      const clickRevenue = ad.cpc || 1.0;
      this.adRevenue += clickRevenue;
      
      // Analytics
      await AnalyticsService.trackEvent('ad_click', {
        ad_id: ad.id,
        category: ad.category,
        cpc: ad.cpc,
        revenue: clickRevenue,
      });
      
      // Uložení dat
      await this.saveAdData();
      
      console.log(`💰 Ad click: ${ad.id}, Revenue: $${clickRevenue.toFixed(2)}`);
      
      // Bonus za high-value clicks
      if (clickRevenue > 2.0) {
        console.log('🎉 High-value ad click!');
      }
      
    } catch (error) {
      console.error('Error tracking ad click:', error);
    }
  }

  async saveAdData() {
    try {
      const data = {
        revenue: this.adRevenue,
        clicks: this.adClicks,
        impressions: this.adImpressions,
        lastUpdated: Date.now(),
      };
      
      await AsyncStorage.setItem('ad_service_data', JSON.stringify(data));
      
    } catch (error) {
      console.error('Error saving ad data:', error);
    }
  }

  startAdBidding() {
    // Simulace real-time bidding každých 30 sekund
    setInterval(() => {
      this.optimizeAdSelection();
    }, 30000);
  }

  optimizeAdSelection() {
    // Simulace optimalizace na základě performance
    const ctr = this.adClicks / Math.max(this.adImpressions, 1); // Click-through rate
    const rpm = (this.adRevenue / Math.max(this.adImpressions, 1)) * 1000; // Revenue per mille
    
    console.log(`📊 Ad Performance: CTR: ${(ctr * 100).toFixed(2)}%, RPM: $${rpm.toFixed(2)}`);
    
    // Pokud je CTR nízký, můžeme upravit algoritmus
    if (ctr < 0.02) { // Méně než 2%
      console.log('⚠️ Low CTR detected, optimizing ad selection...');
    }
  }

  // Getter pro statistiky (pro admin dashboard)
  getAdStats() {
    return {
      totalRevenue: this.adRevenue,
      totalClicks: this.adClicks,
      totalImpressions: this.adImpressions,
      ctr: this.adClicks / Math.max(this.adImpressions, 1),
      rpm: (this.adRevenue / Math.max(this.adImpressions, 1)) * 1000,
      averageCpc: this.adRevenue / Math.max(this.adClicks, 1),
    };
  }

  // Simulace A/B testování reklam
  async runAdExperiment(adVariants) {
    // Implementace A/B testů pro optimalizaci
    console.log('🧪 Running ad experiment with variants:', adVariants.length);
  }
}

export default new AdService();
