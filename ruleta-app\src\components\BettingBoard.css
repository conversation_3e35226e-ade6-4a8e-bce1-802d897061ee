.betting-board {
  background: linear-gradient(135deg, #0d4a2d, #1a5f4a);
  border-radius: 20px;
  padding: 25px;
  border: 3px solid #FFD700;
  box-shadow:
    0 0 30px rgba(255, 215, 0, 0.4),
    inset 0 0 20px rgba(0, 0, 0, 0.3);
  margin: 20px 0;
}

.betting-table {
  display: flex;
  flex-direction: column;
  gap: 3px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 15px;
  padding: 15px;
  border: 2px solid #2d5a3d;
}

.zero-section {
  display: flex;
  justify-content: center;
  margin-bottom: 3px;
}

.zero-section .number-cell {
  width: 100%;
  max-width: 600px;
  height: 45px;
  background: linear-gradient(45deg, #228B22, #006400) !important;
  border: 2px solid #FFD700;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
}

.main-grid {
  display: flex;
  gap: 3px;
}

.numbers-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.number-row {
  display: flex;
  gap: 3px;
}

.columns-section {
  display: flex;
  flex-direction: column;
  gap: 3px;
  width: 60px;
}

.number-cell {
  position: relative;
  flex: 1;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: 2px solid #FFD700;
  border-radius: 6px;
  font-weight: bold;
  font-size: 16px;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  transition: all 0.3s ease;
  user-select: none;
  min-width: 45px;
}

.number-cell:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 215, 0, 0.6);
  border-color: #FFF;
}

.number-cell.red {
  background: linear-gradient(45deg, #DC143C, #B22222);
  border-color: #FFD700;
}

.number-cell.black {
  background: linear-gradient(45deg, #2F2F2F, #000000);
  border-color: #FFD700;
}

.number-cell.green {
  background: linear-gradient(45deg, #228B22, #006400);
  border-color: #FFD700;
}

.number-cell.has-bet {
  border-color: #00FF00;
  box-shadow: 0 0 15px rgba(0, 255, 0, 0.6);
  transform: scale(1.05);
}

.bet-chip {
  position: absolute;
  top: -10px;
  right: -10px;
  background: linear-gradient(45deg, #FFD700, #FFA500);
  color: #000;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
  border: 2px solid #FF6B35;
  box-shadow:
    0 0 10px rgba(255, 107, 53, 0.6),
    0 2px 4px rgba(0, 0, 0, 0.3);
  z-index: 10;
  animation: chipPulse 0.3s ease-out;
}

@keyframes chipPulse {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.dozens-section {
  display: flex;
  gap: 3px;
  margin: 3px 0;
}

.dozen-bet {
  position: relative;
  flex: 1;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(45deg, #4a90e2, #357abd);
  border: 2px solid #FFD700;
  border-radius: 6px;
  cursor: pointer;
  font-weight: bold;
  font-size: 14px;
  color: white;
  transition: all 0.3s ease;
  user-select: none;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.column-bet {
  position: relative;
  flex: 1;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(45deg, #4a90e2, #357abd);
  border: 2px solid #FFD700;
  border-radius: 6px;
  cursor: pointer;
  font-weight: bold;
  font-size: 14px;
  color: white;
  transition: all 0.3s ease;
  user-select: none;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.dozen-bet:hover, .column-bet:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(74, 144, 226, 0.6);
  border-color: #FFF;
}

.dozen-bet.has-bet, .column-bet.has-bet {
  border-color: #00FF00;
  box-shadow: 0 0 15px rgba(0, 255, 0, 0.6);
  transform: scale(1.05);
}

.outside-section {
  display: flex;
  gap: 3px;
  margin-top: 3px;
}

.outside-bet {
  position: relative;
  flex: 1;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #FFD700;
  border-radius: 6px;
  cursor: pointer;
  font-weight: bold;
  font-size: 14px;
  color: white;
  transition: all 0.3s ease;
  user-select: none;
  background: linear-gradient(45deg, #666, #444);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.outside-bet:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 215, 0, 0.6);
  border-color: #FFF;
}

.outside-bet.has-bet {
  border-color: #00FF00;
  box-shadow: 0 0 15px rgba(0, 255, 0, 0.6);
  transform: scale(1.05);
}

.outside-bet.red {
  background: linear-gradient(45deg, #DC143C, #B22222);
  font-size: 20px;
}

.outside-bet.black {
  background: linear-gradient(45deg, #2F2F2F, #000000);
  font-size: 20px;
}

.outside-bet span {
  text-align: center;
  font-weight: bold;
}

@media (max-width: 768px) {
  .betting-board {
    padding: 10px;
  }

  .number-cell {
    width: 35px;
    height: 35px;
    font-size: 12px;
  }

  .outside-bet {
    height: 40px;
  }

  .outside-bet span {
    font-size: 10px;
  }

  .dozen-bet, .column-bet {
    height: 35px;
    font-size: 12px;
  }
}
