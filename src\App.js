import React, {useEffect} from 'react';
import {
  StatusBar,
  StyleSheet,
  Platform,
  PermissionsAndroid,
} from 'react-native';
import {NavigationContainer} from '@react-navigation/native';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';

// Screens
import HomeScreen from './screens/HomeScreen';
import MeditationScreen from './screens/MeditationScreen';
import ProgressScreen from './screens/ProgressScreen';
import CommunityScreen from './screens/CommunityScreen';
import ProfileScreen from './screens/ProfileScreen';

// Services
import NotificationService from './services/NotificationService';
import AnalyticsService from './services/AnalyticsService';
import AdService from './services/AdService';

const Tab = createBottomTabNavigator();

const App = () => {
  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      // Požádej o oprávnění
      await requestPermissions();
      
      // Inicializuj služby
      await NotificationService.initialize();
      await AnalyticsService.initialize();
      await AdService.initialize();
      
      // Naplánuj denní připomínky
      NotificationService.scheduleDailyReminders();
      
      console.log('🧠 MindFlow App successfully initialized!');
    } catch (error) {
      console.error('Error initializing app:', error);
    }
  };

  const requestPermissions = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
          PermissionsAndroid.PERMISSIONS.VIBRATE,
        ]);
        
        if (granted['android.permission.POST_NOTIFICATIONS'] === PermissionsAndroid.RESULTS.GRANTED) {
          console.log('Notification permission granted');
        }
      } catch (err) {
        console.warn('Permission request failed:', err);
      }
    }
  };

  const TabBarBackground = ({children}) => (
    <LinearGradient
      colors={['rgba(102, 126, 234, 0.1)', 'rgba(118, 75, 162, 0.1)']}
      style={StyleSheet.absoluteFillObject}>
      {children}
    </LinearGradient>
  );

  return (
    <SafeAreaProvider>
      <StatusBar
        barStyle="light-content"
        backgroundColor="#667eea"
        translucent={true}
      />
      <NavigationContainer>
        <Tab.Navigator
          screenOptions={({route}) => ({
            tabBarIcon: ({focused, color, size}) => {
              let iconName;

              switch (route.name) {
                case 'Home':
                  iconName = 'home';
                  break;
                case 'Meditation':
                  iconName = 'self-improvement';
                  break;
                case 'Progress':
                  iconName = 'trending-up';
                  break;
                case 'Community':
                  iconName = 'people';
                  break;
                case 'Profile':
                  iconName = 'person';
                  break;
                default:
                  iconName = 'help';
              }

              return (
                <Icon
                  name={iconName}
                  size={focused ? 28 : 24}
                  color={color}
                />
              );
            },
            tabBarActiveTintColor: '#667eea',
            tabBarInactiveTintColor: '#8e8e93',
            tabBarStyle: {
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              borderTopWidth: 0,
              elevation: 20,
              shadowColor: '#000',
              shadowOffset: {width: 0, height: -2},
              shadowOpacity: 0.1,
              shadowRadius: 10,
              height: Platform.OS === 'ios' ? 85 : 65,
              paddingBottom: Platform.OS === 'ios' ? 25 : 10,
              paddingTop: 10,
            },
            tabBarLabelStyle: {
              fontSize: 12,
              fontWeight: '600',
              marginTop: 2,
            },
            headerShown: false,
            tabBarBackground: () => <TabBarBackground />,
          })}>
          <Tab.Screen
            name="Home"
            component={HomeScreen}
            options={{
              tabBarLabel: 'Domů',
            }}
          />
          <Tab.Screen
            name="Meditation"
            component={MeditationScreen}
            options={{
              tabBarLabel: 'Meditace',
            }}
          />
          <Tab.Screen
            name="Progress"
            component={ProgressScreen}
            options={{
              tabBarLabel: 'Pokrok',
            }}
          />
          <Tab.Screen
            name="Community"
            component={CommunityScreen}
            options={{
              tabBarLabel: 'Komunita',
            }}
          />
          <Tab.Screen
            name="Profile"
            component={ProfileScreen}
            options={{
              tabBarLabel: 'Profil',
            }}
          />
        </Tab.Navigator>
      </NavigationContainer>
    </SafeAreaProvider>
  );
};

export default App;
