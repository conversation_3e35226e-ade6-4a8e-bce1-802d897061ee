<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌌 Universe Simulator - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> V<PERSON>mírná Simulace</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: radial-gradient(circle at center, #000428 0%, #004e92 100%);
            color: #ffffff;
            overflow: hidden;
            cursor: crosshair;
        }

        #gameCanvas {
            display: block;
            background: #000011;
            cursor: crosshair;
        }

        .hud {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 100;
        }

        .top-panel {
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            display: flex;
            justify-content: space-between;
            pointer-events: auto;
        }

        .info-panel {
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid rgba(0, 255, 255, 0.3);
            min-width: 200px;
        }

        .controls-panel {
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid rgba(255, 107, 107, 0.3);
            min-width: 250px;
        }

        .bottom-panel {
            position: absolute;
            bottom: 10px;
            left: 10px;
            right: 10px;
            display: flex;
            justify-content: space-between;
            pointer-events: auto;
        }

        .physics-panel {
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid rgba(76, 175, 80, 0.3);
            flex: 1;
            margin-right: 10px;
        }

        .tools-panel {
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid rgba(255, 193, 7, 0.3);
            min-width: 300px;
        }

        .btn {
            background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 8px 15px;
            margin: 3px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.5);
        }

        .btn.active {
            background: linear-gradient(45deg, #ff6b6b 0%, #ee5a24 100%);
            box-shadow: 0 2px 10px rgba(255, 107, 107, 0.3);
        }

        .btn.danger {
            background: linear-gradient(45deg, #ff4757 0%, #c44569 100%);
        }

        .btn.success {
            background: linear-gradient(45deg, #2ed573 0%, #1e90ff 100%);
        }

        .slider-container {
            margin: 8px 0;
        }

        .slider {
            width: 100%;
            height: 4px;
            border-radius: 2px;
            background: #333;
            outline: none;
            margin: 5px 0;
        }

        .value-display {
            font-size: 11px;
            color: #00ffff;
            font-weight: bold;
        }

        .physics-value {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
            font-size: 11px;
        }

        .physics-label {
            color: #ccc;
        }

        .physics-number {
            color: #00ff00;
            font-family: 'Courier New', monospace;
        }

        .mode-selector {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin: 10px 0;
        }

        .object-info {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            border: 1px solid #00ffff;
            border-radius: 5px;
            padding: 10px;
            font-size: 11px;
            pointer-events: none;
            z-index: 200;
            max-width: 200px;
        }

        .minimap {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 150px;
            height: 150px;
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid #00ffff;
            border-radius: 5px;
            pointer-events: auto;
        }

        .trail {
            position: absolute;
            width: 2px;
            height: 2px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            pointer-events: none;
            animation: fade-trail 5s ease-out forwards;
        }

        @keyframes fade-trail {
            0% { opacity: 1; transform: scale(1); }
            100% { opacity: 0; transform: scale(0); }
        }

        .explosion {
            position: absolute;
            width: 20px;
            height: 20px;
            background: radial-gradient(circle, #ff6b6b 0%, transparent 70%);
            border-radius: 50%;
            pointer-events: none;
            animation: explode 1s ease-out forwards;
        }

        @keyframes explode {
            0% { 
                opacity: 1; 
                transform: scale(0); 
                filter: brightness(2);
            }
            50% {
                opacity: 0.8;
                transform: scale(3);
                filter: brightness(1.5);
            }
            100% { 
                opacity: 0; 
                transform: scale(6); 
                filter: brightness(0.5);
            }
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 5px;
            margin: 10px 0;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 5px;
            border-radius: 3px;
            text-align: center;
            font-size: 10px;
        }

        .warning {
            color: #ff6b6b;
            font-size: 10px;
            margin: 5px 0;
            animation: blink 2s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        .title {
            font-size: 14px;
            font-weight: bold;
            color: #00ffff;
            text-shadow: 0 0 10px #00ffff;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 12px;
            color: #ff6b6b;
            margin-bottom: 8px;
        }

        @media (max-width: 768px) {
            .top-panel, .bottom-panel {
                flex-direction: column;
                gap: 10px;
            }
            
            .physics-panel {
                margin-right: 0;
                margin-bottom: 10px;
            }
        }
    </style>
</head>
<body>
    <canvas id="gameCanvas"></canvas>
    
    <div class="hud">
        <div class="top-panel">
            <div class="info-panel">
                <div class="title">🌌 Universe Simulator</div>
                <div class="physics-value">
                    <span class="physics-label">Čas:</span>
                    <span class="physics-number" id="time-display">0.00 s</span>
                </div>
                <div class="physics-value">
                    <span class="physics-label">Objekty:</span>
                    <span class="physics-number" id="object-count">0</span>
                </div>
                <div class="physics-value">
                    <span class="physics-label">Celková hmotnost:</span>
                    <span class="physics-number" id="total-mass">0 kg</span>
                </div>
                <div class="physics-value">
                    <span class="physics-label">Celková energie:</span>
                    <span class="physics-number" id="total-energy">0 J</span>
                </div>
                <div class="physics-value">
                    <span class="physics-label">FPS:</span>
                    <span class="physics-number" id="fps-display">60</span>
                </div>
            </div>

            <div class="controls-panel">
                <div class="title">🎮 Ovládání</div>
                <div class="mode-selector">
                    <button class="btn active" onclick="setMode('planet')" id="mode-planet">🪐 Planeta</button>
                    <button class="btn" onclick="setMode('star')" id="mode-star">⭐ Hvězda</button>
                    <button class="btn" onclick="setMode('asteroid')" id="mode-asteroid">☄️ Asteroid</button>
                    <button class="btn" onclick="setMode('blackhole')" id="mode-blackhole">🕳️ Černá díra</button>
                </div>
                <div class="mode-selector">
                    <button class="btn" onclick="setMode('ship')" id="mode-ship">🚀 Loď</button>
                    <button class="btn" onclick="setMode('delete')" id="mode-delete">🗑️ Smazat</button>
                    <button class="btn danger" onclick="clearUniverse()">💥 Vymazat vše</button>
                </div>
                <div class="slider-container">
                    <label>🔋 Hmotnost: <span class="value-display" id="mass-value">1.0e24 kg</span></label>
                    <input type="range" class="slider" id="mass-slider" min="20" max="30" value="24" step="0.1" oninput="updateMass(this.value)">
                </div>
                <div class="slider-container">
                    <label>⚡ Rychlost: <span class="value-display" id="velocity-value">0 km/s</span></label>
                    <input type="range" class="slider" id="velocity-slider" min="0" max="50" value="0" step="0.5" oninput="updateVelocity(this.value)">
                </div>
            </div>
        </div>

        <div class="bottom-panel">
            <div class="physics-panel">
                <div class="title">⚗️ Fyzikální konstanty</div>
                <div class="stats-grid">
                    <div class="physics-value">
                        <span class="physics-label">G:</span>
                        <span class="physics-number">6.674e-11</span>
                    </div>
                    <div class="physics-value">
                        <span class="physics-label">c:</span>
                        <span class="physics-number">2.998e8 m/s</span>
                    </div>
                    <div class="physics-value">
                        <span class="physics-label">Δt:</span>
                        <span class="physics-number" id="delta-time">0.016 s</span>
                    </div>
                    <div class="physics-value">
                        <span class="physics-label">Měřítko:</span>
                        <span class="physics-number" id="scale-display">1:1e9</span>
                    </div>
                </div>
                
                <div class="subtitle">🌡️ Simulační parametry</div>
                <div class="slider-container">
                    <label>⏱️ Rychlost času: <span class="value-display" id="time-scale-value">1x</span></label>
                    <input type="range" class="slider" id="time-scale" min="0.1" max="100" value="1" step="0.1" oninput="updateTimeScale(this.value)">
                </div>
                <div class="slider-container">
                    <label>🔍 Zoom: <span class="value-display" id="zoom-value">1.0x</span></label>
                    <input type="range" class="slider" id="zoom-slider" min="0.1" max="10" value="1" step="0.1" oninput="updateZoom(this.value)">
                </div>
                <div class="slider-container">
                    <label>🎯 Gravitace: <span class="value-display" id="gravity-value">100%</span></label>
                    <input type="range" class="slider" id="gravity-slider" min="0" max="200" value="100" step="1" oninput="updateGravity(this.value)">
                </div>
            </div>

            <div class="tools-panel">
                <div class="title">🛠️ Nástroje & Scénáře</div>
                <div class="mode-selector">
                    <button class="btn success" onclick="createSolarSystem()">☀️ Sluneční soustava</button>
                    <button class="btn success" onclick="createBinarySystem()">⭐⭐ Dvojhvězda</button>
                    <button class="btn success" onclick="createGalaxy()">🌌 Galaxie</button>
                </div>
                <div class="mode-selector">
                    <button class="btn" onclick="toggleTrails()">✨ Stopy</button>
                    <button class="btn" onclick="toggleCollisions()">💥 Kolize</button>
                    <button class="btn" onclick="toggleRelativity()">⚡ Relativita</button>
                </div>
                <div class="mode-selector">
                    <button class="btn" onclick="pauseSimulation()">⏸️ Pauza</button>
                    <button class="btn" onclick="saveUniverse()">💾 Uložit</button>
                    <button class="btn" onclick="loadUniverse()">📁 Načíst</button>
                </div>
                <div id="warnings" class="warning" style="display: none;">
                    ⚠️ Nestabilní orbity detekované!
                </div>
            </div>
        </div>
    </div>

    <div class="object-info" id="object-info" style="display: none;">
        <div id="info-content"></div>
    </div>

    <canvas class="minimap" id="minimap"></canvas>

    <script src="universe-physics.js"></script>
    <script src="universe-game.js"></script>
</body>
</html>
