# 🌌 Universe Simulator - Brutální Vesmírná Simulace

Kompletní 2D/3D vesmírná simulace s realistickou fyzikou, gra<PERSON><PERSON><PERSON><PERSON>, hv<PERSON><PERSON>dnou evolucí a všemi fyzikálními veličinami!

## 🚀 **Spuštěn<PERSON> simulace**

### Lokální spuštění:
```bash
python -m http.server 8001
```
Pak otevřete: `http://localhost:8001/universe-simulator.html`

## 🎮 **Ovládání**

### 🖱️ **Myš:**
- **Levé tla<PERSON>ko** - Vytvoření objektu na pozici kurzoru
- **Pravé tla<PERSON>tko** - Kontextové menu objektu
- **Ko<PERSON><PERSON>ko** - Zoom in/out
- **Tažení** - Pohyb kamery po vesmíru

### ⌨️ **Klávesnice:**
- **Mezerník** - Pauza/Pokračování simulace
- **R** - Reset kamery na střed
- **1-5** - <PERSON><PERSON>l<PERSON> výběr typu objektu
- **Delete** - Vymazání celého vesmíru

### 📱 **Touch (mobily):**
- **Tap** - Vytvoření objektu
- **Drag** - Pohyb kamery
- **Pinch** - Zoom

## 🔬 **Typy objektů**

### **🪐 Planety**
- **Hmotnost:** 10²⁰ - 10²⁷ kg
- **Vlastnosti:** Atmosféra, teplota, složení
- **Evoluce:** Ochlazování, atmosférická eroze

### **⭐ Hvězdy**
- **Hmotnost:** 10²⁹ - 10³² kg
- **Vlastnosti:** Jaderná fúze, svítivost, teplota
- **Evoluce:** Hlavní sekvence → Červený obr → Smrt

### **☄️ Asteroidy**
- **Hmotnost:** 10¹⁵ - 10²³ kg
- **Vlastnosti:** Skalnaté složení
- **Chování:** Chaotické orbity

### **🕳️ Černé díry**
- **Hmotnost:** 10³⁰ - 10⁴⁰ kg
- **Vlastnosti:** Event horizon, Hawkingovo vyzařování
- **Efekty:** Gravitační čočky, accretion disk

### **🚀 Vesmírné lodě**
- **Hmotnost:** 10³ - 10⁶ kg
- **Vlastnosti:** Umělé objekty
- **Chování:** Ovladatelné

## ⚗️ **Fyzikální engine**

### **🌍 Gravitace**
```
F = G × m₁ × m₂ / r²
```
- **Newtonova gravitace** pro běžné objekty
- **Relativistické korekce** pro extrémní hmotnosti
- **N-body simulace** s prostorovou optimalizací

### **💥 Kolize a fúze**
- **Zachování hybnosti:** p = m₁v₁ + m₂v₂
- **Zachování energie:** E = ½mv² + mgh
- **Realistické sloučení** objektů
- **Spektakulární exploze**

### **⭐ Hvězdná evoluce**
- **Jaderná fúze:** E = mc²
- **Hvězdný vítr:** Ztráta hmotnosti
- **Supernovy:** Masivní exploze
- **Neutronové hvězdy** a **černé díry**

### **🌡️ Termodynamika**
- **Stefan-Boltzmannův zákon:** L ∝ T⁴
- **Hawkingova teplota:** T ∝ 1/M
- **Ochlazování** objektů v čase

### **⚡ Relativistické efekty**
- **Dilatace času:** Δt' = Δt/γ
- **Gravitační červený posuv**
- **Lorentzovy transformace**

## 🌟 **Předpřipravené scénáře**

### **☀️ Sluneční soustava**
- **Slunce** (1.989×10³⁰ kg)
- **8 planet** s realistickými parametry
- **Stabilní orbity** podle Keplerových zákonů

### **⭐⭐ Dvojhvězda**
- **Dva sluneční objekty** obíhající kolem společného těžiště
- **Komplexní gravitační interakce**
- **Možnost planet** v systému

### **🌌 Galaxie**
- **Centrální černá díra** (Sagittarius A*)
- **200+ hvězd** ve spirálních ramenech
- **Realistické galaktické rotace**

## 📊 **Fyzikální veličiny**

### **Konstanty:**
- **G** = 6.674×10⁻¹¹ m³/kg⋅s²
- **c** = 2.998×10⁸ m/s
- **h** = 6.626×10⁻³⁴ J⋅s
- **k** = 1.381×10⁻²³ J/K

### **Měřené hodnoty:**
- **Celková hmotnost** systému
- **Celková energie** (kinetická + potenciální)
- **Simulační čas** s možností zrychlení
- **FPS** a výkon

### **Detekce:**
- **Nestabilní orbity** (úniková rychlost)
- **Kolizní kurzy**
- **Kritické hustoty**

## 🎯 **Pokročilé funkce**

### **⚙️ Nastavitelné parametry:**
- **Hmotnost objektů:** 10²⁰ - 10⁴⁰ kg
- **Počáteční rychlost:** 0 - 50 km/s
- **Rychlost času:** 0.1x - 100x
- **Gravitační síla:** 0% - 200%
- **Zoom:** 0.1x - 10x

### **🔧 Simulační módy:**
- **✨ Stopy orbit** - Vizualizace trajektorií
- **💥 Kolize** - Zapnutí/vypnutí srážek
- **⚡ Relativita** - Einsteinovy korekce

### **💾 Uložení/načtení:**
- **Export** celého vesmíru do JSON
- **Import** uložených simulací
- **Sdílení** scénářů

## 📈 **Výkon a optimalizace**

### **Prostorová mřížka:**
- **Optimalizace N-body** problému
- **Culling** objektů mimo obrazovku
- **LOD** (Level of Detail) pro vzdálené objekty

### **Rendering:**
- **Canvas 2D** s hardware akcelerací
- **Particle systémy** pro exploze
- **Glow efekty** pro hvězdy
- **Minimap** s přehledem

### **Výkon:**
- **60 FPS** na moderních zařízeních
- **Adaptivní kvalita** podle výkonu
- **Mobilní optimalizace**

## 🎓 **Vzdělávací hodnota**

### **Fyzikální koncepty:**
- **Gravitace** a orbitální mechanika
- **Zachování** hybnosti a energie
- **Relativistické** efekty
- **Hvězdná** evoluce
- **Termodynamika** vesmíru

### **Matematické modely:**
- **Diferenciální rovnice** pohybu
- **Numerická integrace** (Verlet)
- **Vektorová algebra**
- **Exponenciální** funkce

### **Astronomické jevy:**
- **Keplerovy zákony**
- **Chandrasekharova mez**
- **Schwarzschildův poloměr**
- **Hawkingovo vyzařování**

## 🔬 **Vědecká přesnost**

### **✅ Fyzikálně správné:**
- **Newtonova gravitace** pro běžné objekty
- **Relativistické korekce** pro extrémní případy
- **Zachování** fyzikálních zákonů
- **Realistické** časové škály (s možností zrychlení)

### **⚠️ Zjednodušení:**
- **2D simulace** místo 3D prostoru
- **Zanedbání** kvantových efektů
- **Aproximace** složitých procesů
- **Vizuální** reprezentace neviditelných jevů

## 🌟 **Příklady experimentů**

### **🔬 Základní experimenty:**
1. **Vytvoř Zemi a Měsíc** - Pozoruj stabilní orbitu
2. **Dvojhvězda** - Sleduj komplexní tanec
3. **Asteroid impact** - Simuluj srážku s planetou
4. **Černá díra** - Pozoruj gravitační efekty

### **🚀 Pokročilé scénáře:**
1. **Galaktická kolize** - Dvě galaxie se srazí
2. **Supernova** - Sleduj smrt masivní hvězdy
3. **Planetární systém** - Vytvoř vlastní sluneční soustavu
4. **Relativistické efekty** - Testuj Einsteinovu teorii

## 🎮 **Herní aspekty**

### **🏆 Výzvy:**
- **Stabilní orbita** - Vytvoř planetu s 1000+ orbitami
- **Galaktický architekt** - Postav galaxii se 100+ hvězdami
- **Černá díra** - Pohltí 10 objektů
- **Supernova** - Způsob hvězdnou explozi

### **📊 Statistiky:**
- **Počet vytvořených objektů**
- **Celkový simulační čas**
- **Největší dosažená hmotnost**
- **Nejdelší stabilní orbita**

---

## 🚀 **Začněte experimentovat!**

1. **Otevřete simulaci** v prohlížeči
2. **Vyberte "🪐 Planeta"** v ovládání
3. **Klikněte** kamkoliv pro vytvoření planety
4. **Přidejte hvězdu** pro gravitační interakci
5. **Sledujte** jak se formují orbity!

**Užijte si brutální vesmírnou simulaci s realistickou fyzikou! 🌌**

*Simulace je open source a slouží vzdělávacím i zábavním účelům.*
