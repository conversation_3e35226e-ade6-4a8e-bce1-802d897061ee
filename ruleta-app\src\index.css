* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Arial', sans-serif;
  background: linear-gradient(135deg, #0f4c3a, #1a5f4a);
  color: white;
  min-height: 100vh;
}

.app {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h1 {
  font-size: 3rem;
  color: #ffd700;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
  margin-bottom: 10px;
}

.header p {
  font-size: 1.2rem;
  opacity: 0.9;
}

.game-container {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 30px;
  margin-bottom: 30px;
}

@media (max-width: 1200px) {
  .game-container {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

.roulette-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.side-panel {
  background: rgba(255,255,255,0.1);
  border-radius: 15px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.2);
}

.betting-board {
  width: 100%;
  max-width: 800px;
  margin-top: 20px;
}

.controls {
  display: flex;
  gap: 15px;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  margin: 20px 0;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
}

.btn-primary {
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255,107,53,0.4);
}

.btn-secondary {
  background: linear-gradient(45deg, #4a90e2, #357abd);
  color: white;
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(74,144,226,0.4);
}

.btn-strategy {
  background: linear-gradient(45deg, #9b59b6, #8e44ad);
  color: white;
}

.btn-strategy:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(155,89,182,0.4);
}

.input-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.input-group label {
  font-weight: bold;
  min-width: 80px;
}

.input-group input {
  padding: 8px 12px;
  border: 2px solid rgba(255,255,255,0.3);
  border-radius: 6px;
  background: rgba(255,255,255,0.1);
  color: white;
  font-size: 1rem;
  width: 100px;
}

.input-group input:focus {
  outline: none;
  border-color: #ffd700;
  box-shadow: 0 0 10px rgba(255,215,0,0.3);
}

.result-display {
  text-align: center;
  margin: 20px 0;
  padding: 20px;
  background: rgba(0,0,0,0.3);
  border-radius: 10px;
  border: 2px solid #ffd700;
}

.winning-number {
  font-size: 3rem;
  font-weight: bold;
  color: #ffd700;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.balance-display {
  font-size: 1.5rem;
  margin: 10px 0;
}

.balance-positive {
  color: #4caf50;
}

.balance-negative {
  color: #f44336;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-top: 20px;
}

.stat-item {
  background: rgba(0,0,0,0.3);
  padding: 15px;
  border-radius: 8px;
  text-align: center;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #ffd700;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.8;
  margin-top: 5px;
}
