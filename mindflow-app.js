// MindFlow App - JavaScript funkcionalita
class MindFlowApp {
    constructor() {
        this.currentMood = null;
        this.meditationCount = 12;
        this.moodScore = 8.2;
        this.streakDays = 7;
        this.currentTab = 'home';
        
        // Reklamy pro monetizaci
        this.ads = [
            {
                text: "🎧 Spotify Premium - 3 měsíce zdar<PERSON>!",
                url: "https://spotify.com",
                type: "music"
            },
            {
                text: "📚 Audible - Poslouchej knihy o mindfulness",
                url: "https://audible.com",
                type: "books"
            },
            {
                text: "🛏️ Emma matrace - Lepší spánek pro lepší život",
                url: "https://emma-sleep.com",
                type: "sleep"
            },
            {
                text: "🧘‍♀️ Headspace Premium - Meditace pro každý den",
                url: "https://headspace.com",
                type: "meditation"
            },
            {
                text: "💊 Vitamíny pro mozek - Podpoř svou koncentraci",
                url: "#",
                type: "health"
            }
        ];
        
        this.init();
    }
    
    init() {
        this.updateStats();
        this.rotateAds();
        this.startDailyReminders();
        
        // Simulace real-time dat
        setInterval(() => {
            this.updateRealTimeData();
        }, 30000); // Každých 30 sekund
        
        console.log('🧠 MindFlow App inicializována!');
    }
    
    selectMood(button, mood) {
        // Odstraň předchozí výběr
        document.querySelectorAll('.mood-btn').forEach(btn => {
            btn.classList.remove('selected');
        });
        
        // Vyber novou náladu
        button.classList.add('selected');
        this.currentMood = mood;
        
        // Uložení do localStorage
        localStorage.setItem('mindflow_mood_' + new Date().toDateString(), mood);
        
        // Feedback podle nálady
        const moodMessages = {
            amazing: "Skvělé! Pokračuj v tom, co děláš! ✨",
            good: "Krásně! Máš dobrý den! 😊",
            okay: "V pořádku. Možná ti pomůže krátká meditace? 🧘‍♀️",
            bad: "Chápu. Zkus dýchací cvičení, může to pomoct 🫁",
            terrible: "Je mi líto. Jsi silnější než si myslíš. Jsme tu pro tebe 💙"
        };
        
        this.showNotification(moodMessages[mood]);
        this.updateMoodScore(mood);
        
        // Analytics pro zlepšení aplikace
        this.trackMoodSelection(mood);
    }
    
    updateMoodScore(mood) {
        const moodValues = {
            amazing: 10,
            good: 8,
            okay: 6,
            bad: 4,
            terrible: 2
        };
        
        // Aktualizuj průměrnou náladu
        const currentScore = this.moodScore;
        const newValue = moodValues[mood];
        this.moodScore = ((currentScore * 6) + newValue) / 7; // Průměr za týden
        
        document.getElementById('mood-score').textContent = this.moodScore.toFixed(1);
        
        // Animace změny
        this.animateStatChange('mood-score');
    }
    
    openFeature(feature) {
        const features = {
            meditation: {
                title: "🧘‍♀️ Meditace",
                message: "Spouštím 10minutovou meditaci pro začátečníky...",
                action: () => this.startMeditation()
            },
            breathing: {
                title: "🫁 Dýchací cvičení",
                message: "Připravuji 4-7-8 dýchací techniku...",
                action: () => this.startBreathing()
            },
            journal: {
                title: "📝 Deník myšlenek",
                message: "Otevírám tvůj osobní deník...",
                action: () => this.openJournal()
            },
            sleep: {
                title: "😴 Spánkové příběhy",
                message: "Vybírám uklidňující příběh na dobrou noc...",
                action: () => this.startSleepStory()
            }
        };
        
        const selectedFeature = features[feature];
        if (selectedFeature) {
            this.showNotification(selectedFeature.message);
            
            // Spustí funkci po 2 sekundách
            setTimeout(() => {
                selectedFeature.action();
            }, 2000);
        }
    }
    
    startMeditation() {
        this.meditationCount++;
        document.getElementById('meditation-count').textContent = this.meditationCount;
        this.animateStatChange('meditation-count');
        
        this.showNotification("🧘‍♀️ Meditace dokončena! Cítíš se lépe?");
        
        // Zvýš streak
        this.updateStreak();
        
        // Zobraz reklamu po meditaci (monetizace)
        setTimeout(() => {
            this.showTargetedAd('meditation');
        }, 3000);
    }
    
    startBreathing() {
        this.showNotification("🫁 Dýchej se mnou: Nadechni (4s) → Zadrž (7s) → Vydechni (8s)");
        
        // Simulace dýchacího cvičení
        let cycle = 0;
        const breathingInterval = setInterval(() => {
            cycle++;
            if (cycle <= 3) {
                this.showNotification(`🫁 Cyklus ${cycle}/3: Pokračuj v dýchání...`);
            } else {
                clearInterval(breathingInterval);
                this.showNotification("✨ Dýchací cvičení dokončeno! Cítíš se klidněji?");
            }
        }, 19000); // 19 sekund na cyklus (4+7+8)
    }
    
    openJournal() {
        const journalPrompts = [
            "Za co jsi dnes vděčný/á?",
            "Jaký byl nejlepší moment dnešního dne?",
            "Co tě dnes inspirovalo?",
            "Jak se dnes cítíš a proč?",
            "Co bys chtěl/a zítra udělat jinak?"
        ];
        
        const randomPrompt = journalPrompts[Math.floor(Math.random() * journalPrompts.length)];
        this.showNotification(`📝 Dnešní otázka k zamyšlení: "${randomPrompt}"`);
    }
    
    startSleepStory() {
        const sleepStories = [
            "🌙 Procházka měsíčním lesem",
            "🏔️ Klidné jezero v horách",
            "🌊 Vlny na tichém pobřeží",
            "🌸 Zahrada plná levandule",
            "⭐ Hvězdná obloha nad polem"
        ];
        
        const randomStory = sleepStories[Math.floor(Math.random() * sleepStories.length)];
        this.showNotification(`😴 Přehrávám: "${randomStory}"`);
        
        // Zobraz reklamu na spánkové produkty
        setTimeout(() => {
            this.showTargetedAd('sleep');
        }, 5000);
    }
    
    quickMeditation() {
        this.showNotification("🚀 Spouštím 3minutovou rychlou meditaci...");
        
        setTimeout(() => {
            this.startMeditation();
        }, 1000);
    }
    
    updateStreak() {
        const today = new Date().toDateString();
        const lastActivity = localStorage.getItem('mindflow_last_activity');
        
        if (lastActivity !== today) {
            this.streakDays++;
            document.getElementById('streak-days').textContent = this.streakDays;
            this.animateStatChange('streak-days');
            localStorage.setItem('mindflow_last_activity', today);
        }
    }
    
    switchTab(element, tab) {
        // Odstraň aktivní třídu ze všech tabů
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        
        // Aktivuj vybraný tab
        element.classList.add('active');
        this.currentTab = tab;
        
        // Simulace změny obsahu
        const tabMessages = {
            home: "🏠 Vítej zpět v MindFlow!",
            progress: "📊 Tvůj pokrok je úžasný! Pokračuj!",
            community: "👥 Připojuješ se ke komunitě 2M+ uživatelů",
            profile: "👤 Upravuješ svůj profil"
        };
        
        this.showNotification(tabMessages[tab]);
    }
    
    showNotification(message) {
        const notification = document.getElementById('notification');
        const notificationText = document.getElementById('notification-text');
        
        notificationText.textContent = message;
        notification.classList.add('show');
        
        // Skryj po 3 sekundách
        setTimeout(() => {
            notification.classList.remove('show');
        }, 3000);
    }
    
    animateStatChange(elementId) {
        const element = document.getElementById(elementId);
        element.style.transform = 'scale(1.2)';
        element.style.color = '#ffd700';
        
        setTimeout(() => {
            element.style.transform = 'scale(1)';
            element.style.color = 'white';
        }, 500);
    }
    
    rotateAds() {
        const adBanner = document.querySelector('.ad-banner');
        let currentAdIndex = 0;
        
        setInterval(() => {
            currentAdIndex = (currentAdIndex + 1) % this.ads.length;
            const ad = this.ads[currentAdIndex];
            
            // Animace změny reklamy
            adBanner.style.opacity = '0';
            
            setTimeout(() => {
                adBanner.querySelector('.ad-text').textContent = ad.text;
                adBanner.onclick = () => {
                    this.trackAdClick(ad);
                    this.showNotification('Děkujeme za podporu! 💝');
                };
                adBanner.style.opacity = '1';
            }, 300);
            
        }, 10000); // Změna reklamy každých 10 sekund
    }
    
    showTargetedAd(category) {
        const targetedAds = this.ads.filter(ad => ad.type === category);
        if (targetedAds.length > 0) {
            const ad = targetedAds[Math.floor(Math.random() * targetedAds.length)];
            this.showNotification(`💡 ${ad.text}`);
        }
    }
    
    trackAdClick(ad) {
        // Analytics pro sledování efektivity reklam
        console.log(`Ad clicked: ${ad.type} - ${ad.text}`);
        
        // Zde by se poslala data na server pro monetizaci
        // fetch('/api/ad-click', { method: 'POST', body: JSON.stringify(ad) });
    }
    
    trackMoodSelection(mood) {
        // Analytics pro zlepšení aplikace
        console.log(`Mood selected: ${mood} at ${new Date().toISOString()}`);
        
        // Uložení pro personalizaci
        const moodHistory = JSON.parse(localStorage.getItem('mindflow_mood_history') || '[]');
        moodHistory.push({ mood, timestamp: Date.now() });
        
        // Zachovej pouze posledních 30 záznamů
        if (moodHistory.length > 30) {
            moodHistory.shift();
        }
        
        localStorage.setItem('mindflow_mood_history', JSON.stringify(moodHistory));
    }
    
    updateRealTimeData() {
        // Simulace real-time aktualizací
        const variations = [-0.1, 0, 0.1, 0.2];
        const variation = variations[Math.floor(Math.random() * variations.length)];
        
        if (this.moodScore + variation >= 1 && this.moodScore + variation <= 10) {
            this.moodScore += variation;
            document.getElementById('mood-score').textContent = this.moodScore.toFixed(1);
        }
    }
    
    startDailyReminders() {
        // Simulace push notifikací
        const reminders = [
            "🌅 Dobré ráno! Jak se dnes cítíš?",
            "☕ Čas na krátkou pauzu a dýchací cvičení",
            "🌅 Večerní reflexe: Co bylo dnes pozitivního?",
            "😴 Čas na uklidňující spánkový příběh"
        ];
        
        // Zobraz připomínku každých 2 minuty (pro demo)
        setInterval(() => {
            const reminder = reminders[Math.floor(Math.random() * reminders.length)];
            this.showNotification(reminder);
        }, 120000);
    }
    
    updateStats() {
        // Načti uložená data
        const savedMoodHistory = JSON.parse(localStorage.getItem('mindflow_mood_history') || '[]');
        const savedMeditationCount = localStorage.getItem('mindflow_meditation_count');
        const savedStreakDays = localStorage.getItem('mindflow_streak_days');
        
        if (savedMeditationCount) {
            this.meditationCount = parseInt(savedMeditationCount);
            document.getElementById('meditation-count').textContent = this.meditationCount;
        }
        
        if (savedStreakDays) {
            this.streakDays = parseInt(savedStreakDays);
            document.getElementById('streak-days').textContent = this.streakDays;
        }
        
        // Vypočítej průměrnou náladu z historie
        if (savedMoodHistory.length > 0) {
            const moodValues = { amazing: 10, good: 8, okay: 6, bad: 4, terrible: 2 };
            const totalMood = savedMoodHistory.reduce((sum, entry) => sum + moodValues[entry.mood], 0);
            this.moodScore = totalMood / savedMoodHistory.length;
            document.getElementById('mood-score').textContent = this.moodScore.toFixed(1);
        }
    }
}

// Globální funkce pro HTML
let app;

function selectMood(button, mood) {
    if (app) app.selectMood(button, mood);
}

function openFeature(feature) {
    if (app) app.openFeature(feature);
}

function quickMeditation() {
    if (app) app.quickMeditation();
}

function switchTab(element, tab) {
    if (app) app.switchTab(element, tab);
}

function showNotification(message) {
    if (app) app.showNotification(message);
}

// Inicializace při načtení stránky
document.addEventListener('DOMContentLoaded', () => {
    app = new MindFlowApp();
    
    // Uvítací zpráva
    setTimeout(() => {
        app.showNotification('🧠 Vítej v MindFlow! Tvoje cesta k lepšímu mentálnímu zdraví začíná zde.');
    }, 1000);
});
