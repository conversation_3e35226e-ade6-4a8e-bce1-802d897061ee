.strategy-panel {
  margin-top: 20px;
}

.strategy-panel h3 {
  color: #FFD700;
  margin-bottom: 10px;
  text-align: center;
  font-size: 1.3rem;
}

.strategy-description {
  font-size: 0.9rem;
  opacity: 0.9;
  margin-bottom: 15px;
  text-align: center;
  line-height: 1.4;
}

.strategy-list {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 15px;
}

.strategy-item {
  background: rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.strategy-item:hover {
  border-color: rgba(255, 215, 0, 0.5);
  background: rgba(255, 215, 0, 0.1);
}

.strategy-item.selected {
  border-color: #FFD700;
  background: rgba(255, 215, 0, 0.2);
  box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.strategy-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.strategy-header h4 {
  color: #FFD700;
  margin: 0;
  font-size: 1rem;
}

.strategy-cost {
  background: rgba(255, 215, 0, 0.2);
  color: #FFD700;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: bold;
}

.strategy-desc {
  font-size: 0.85rem;
  opacity: 0.9;
  margin-bottom: 8px;
  line-height: 1.3;
}

.strategy-bets {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.bet-preview {
  background: rgba(74, 144, 226, 0.3);
  color: #4a90e2;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: bold;
  border: 1px solid rgba(74, 144, 226, 0.5);
}

.strategy-controls {
  text-align: center;
  margin-bottom: 15px;
}

.strategy-controls .btn {
  width: 100%;
  margin-bottom: 10px;
}

.selected-strategy-info {
  background: rgba(255, 215, 0, 0.1);
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: 6px;
  padding: 8px;
  color: #FFD700;
  font-size: 0.9rem;
  text-align: center;
}

.strategy-tips {
  background: rgba(0, 0, 0, 0.4);
  border-radius: 8px;
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.strategy-tips h4 {
  color: #FFD700;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.strategy-tips ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.strategy-tips li {
  font-size: 0.8rem;
  margin-bottom: 5px;
  opacity: 0.9;
  line-height: 1.3;
}

.strategy-tips strong {
  color: #4a90e2;
}

/* Scrollbar styling */
.strategy-list::-webkit-scrollbar {
  width: 6px;
}

.strategy-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.strategy-list::-webkit-scrollbar-thumb {
  background: rgba(255, 215, 0, 0.5);
  border-radius: 3px;
}

.strategy-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 215, 0, 0.7);
}

@media (max-width: 768px) {
  .strategy-panel h3 {
    font-size: 1.1rem;
  }
  
  .strategy-item {
    padding: 10px;
  }
  
  .strategy-header h4 {
    font-size: 0.9rem;
  }
  
  .strategy-desc {
    font-size: 0.8rem;
  }
  
  .bet-preview {
    font-size: 0.65rem;
  }
  
  .strategy-tips {
    padding: 10px;
  }
  
  .strategy-tips h4 {
    font-size: 0.85rem;
  }
  
  .strategy-tips li {
    font-size: 0.75rem;
  }
}
