﻿# đź§  MindFlow - Test Checklist

## đź“± **Quick Start**
```
./demo.bat  # SpustĂ­ demo aplikaci (Windows)
```

## âś… **Test Scenarios**

### **1. Mood Tracking Test**
- [ ] OtevĹ™i aplikaci
- [ ] Klikni na rĹŻznĂ© nĂˇlady (đź¤©đźŠđźđź”đź˘)
- [ ] Zkontroluj, Ĺľe se nĂˇlada uloĹľĂ­
- [ ] OvÄ›Ĺ™ analytics v konzoli
- [ ] Zkontroluj streak counter

### **2. Ad Integration Test**
- [ ] Zkontroluj banner reklamu nahoĹ™e
- [ ] PoÄŤkej 5 sekund na rotaci reklamy
- [ ] Klikni na reklamu (test ad - bezpeÄŤnĂ©)
- [ ] OvÄ›Ĺ™ revenue tracking v konzoli
- [ ] Zkontroluj rĹŻznĂ© typy reklam

### **3. Navigation Test**
- [ ] PĹ™epni mezi vĹˇemi 5 taby
- [ ] Zkontroluj smooth animace
- [ ] OvÄ›Ĺ™, Ĺľe se obsah naÄŤĂ­tĂˇ
- [ ] Test zpÄ›t tlaÄŤĂ­tka

### **4. Meditation Test**
- [ ] Jdi na Meditation tab
- [ ] SpusĹĄ quick meditation
- [ ] Test pause/resume (pokud implementovĂˇno)
- [ ] Zkontroluj progress update

### **5. Premium Features Test**
- [ ] Zkus pĹ™Ă­stup k premium funkci
- [ ] OvÄ›Ĺ™ upgrade prompt
- [ ] Test subscription flow
- [ ] Zkontroluj premium badge

## đź’ˇ **Test Tips**
- VĹˇechny reklamy jsou testovacĂ­ (bezpeÄŤnĂ© klikat)
- Analytics se logujĂ­ do konzole
- Fast mode = rychlejĹˇĂ­ timers pro testovĂˇnĂ­
- Mock data jsou pĹ™ednastavenĂˇ
- Test user: <EMAIL>

## đźŽŻ **Success Criteria**
- [ ] Aplikace se spustĂ­ bez crashĹŻ
- [ ] VĹˇechny funkce fungujĂ­
- [ ] Reklamy se zobrazujĂ­ a rotujĂ­
- [ ] Analytics trackujĂ­ eventy
- [ ] Data se uklĂˇdajĂ­ sprĂˇvnÄ›
- [ ] UI je responsive
- [ ] Performance je dobrĂˇ (>30 FPS)
