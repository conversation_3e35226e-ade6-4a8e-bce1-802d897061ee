.game-stats {
  margin-top: 20px;
}

.game-stats h3 {
  color: #FFD700;
  margin-bottom: 15px;
  text-align: center;
  font-size: 1.3rem;
}

.game-stats h4 {
  color: #FFD700;
  margin-bottom: 10px;
  font-size: 1rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  margin-bottom: 20px;
}

.stat-item {
  background: rgba(0, 0, 0, 0.3);
  padding: 12px;
  border-radius: 8px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-value {
  font-size: 1.3rem;
  font-weight: bold;
  color: #FFD700;
  margin-bottom: 5px;
}

.stat-value.positive {
  color: #4CAF50;
}

.stat-value.negative {
  color: #F44336;
}

.stat-label {
  font-size: 0.8rem;
  opacity: 0.8;
}

.recent-numbers {
  margin-bottom: 20px;
}

.numbers-row {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
  justify-content: center;
}

.recent-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  color: white;
  border: 2px solid #FFD700;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.recent-number.red {
  background: linear-gradient(45deg, #DC143C, #B22222);
}

.recent-number.black {
  background: linear-gradient(45deg, #2F2F2F, #000000);
}

.recent-number.green {
  background: linear-gradient(45deg, #228B22, #006400);
}

.hot-numbers {
  margin-bottom: 20px;
}

.hot-numbers-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.hot-number-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(0, 0, 0, 0.3);
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.hot-number {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: bold;
  color: white;
  border: 1px solid #FFD700;
}

.hot-number.red {
  background: linear-gradient(45deg, #DC143C, #B22222);
}

.hot-number.black {
  background: linear-gradient(45deg, #2F2F2F, #000000);
}

.hot-number.green {
  background: linear-gradient(45deg, #228B22, #006400);
}

.hot-count {
  font-size: 0.9rem;
  color: #4a90e2;
  font-weight: bold;
}

.game-history {
  margin-bottom: 20px;
}

.history-list {
  max-height: 200px;
  overflow-y: auto;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(0, 0, 0, 0.3);
  padding: 10px;
  border-radius: 6px;
  margin-bottom: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.history-number .number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  color: white;
  border: 2px solid #FFD700;
}

.history-number .number.red {
  background: linear-gradient(45deg, #DC143C, #B22222);
}

.history-number .number.black {
  background: linear-gradient(45deg, #2F2F2F, #000000);
}

.history-number .number.green {
  background: linear-gradient(45deg, #228B22, #006400);
}

.history-details {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.history-time {
  font-size: 0.8rem;
  opacity: 0.8;
}

.history-result {
  font-weight: bold;
  font-size: 0.9rem;
}

.history-result.win {
  color: #4CAF50;
}

.history-result.loss {
  color: #F44336;
}

.no-history {
  text-align: center;
  padding: 20px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.no-history p {
  margin-bottom: 8px;
  opacity: 0.8;
}

.no-history p:first-child {
  font-size: 1.1rem;
  color: #FFD700;
}

/* Scrollbar styling */
.history-list::-webkit-scrollbar {
  width: 6px;
}

.history-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.history-list::-webkit-scrollbar-thumb {
  background: rgba(255, 215, 0, 0.5);
  border-radius: 3px;
}

.history-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 215, 0, 0.7);
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .stat-item {
    padding: 10px;
  }
  
  .stat-value {
    font-size: 1.1rem;
  }
  
  .recent-number, .hot-number, .history-number .number {
    width: 25px;
    height: 25px;
    font-size: 10px;
  }
  
  .history-item {
    padding: 8px;
    gap: 10px;
  }
  
  .history-details {
    font-size: 0.85rem;
  }
}
