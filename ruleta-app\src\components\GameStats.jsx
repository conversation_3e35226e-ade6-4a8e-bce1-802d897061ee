import React from 'react'
import './GameStats.css'

const GameStats = ({ totalWins, totalLosses, gameHistory, balance, initialBalance }) => {
  const totalGames = totalWins + totalLosses
  const winRate = totalGames > 0 ? ((totalWins / totalGames) * 100).toFixed(1) : 0
  const profitLoss = balance - initialBalance
  const isProfitable = profitLoss >= 0

  const getNumberColor = (number) => {
    if (number === 0) return 'green'
    const redNumbers = [1,3,5,7,9,12,14,16,18,19,21,23,25,27,30,32,34,36]
    return redNumbers.includes(number) ? 'red' : 'black'
  }

  const getRecentNumbers = () => {
    return gameHistory.slice(0, 10).map(game => game.number)
  }

  const getHotNumbers = () => {
    const numberCounts = {}
    gameHistory.forEach(game => {
      numberCounts[game.number] = (numberCounts[game.number] || 0) + 1
    })
    
    return Object.entries(numberCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([number, count]) => ({ number: parseInt(number), count }))
  }

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString('cs-CZ', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="game-stats">
      <h3>📊 Statistiky Hry</h3>
      
      <div className="stats-grid">
        <div className="stat-item">
          <div className="stat-value">{totalGames}</div>
          <div className="stat-label">Celkem her</div>
        </div>
        
        <div className="stat-item">
          <div className="stat-value">{winRate}%</div>
          <div className="stat-label">Úspěšnost</div>
        </div>
        
        <div className="stat-item">
          <div className={`stat-value ${isProfitable ? 'positive' : 'negative'}`}>
            {isProfitable ? '+' : ''}{profitLoss.toLocaleString()}
          </div>
          <div className="stat-label">Zisk/Ztráta</div>
        </div>
        
        <div className="stat-item">
          <div className="stat-value">{totalWins}/{totalLosses}</div>
          <div className="stat-label">Výhry/Prohry</div>
        </div>
      </div>

      {gameHistory.length > 0 && (
        <>
          <div className="recent-numbers">
            <h4>🎯 Poslední čísla</h4>
            <div className="numbers-row">
              {getRecentNumbers().map((number, index) => (
                <div 
                  key={index}
                  className={`recent-number ${getNumberColor(number)}`}
                >
                  {number}
                </div>
              ))}
            </div>
          </div>

          {getHotNumbers().length > 0 && (
            <div className="hot-numbers">
              <h4>🔥 Nejčastější čísla</h4>
              <div className="hot-numbers-list">
                {getHotNumbers().map(({ number, count }) => (
                  <div key={number} className="hot-number-item">
                    <span className={`hot-number ${getNumberColor(number)}`}>
                      {number}
                    </span>
                    <span className="hot-count">{count}x</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="game-history">
            <h4>📝 Historie her</h4>
            <div className="history-list">
              {gameHistory.slice(0, 5).map((game, index) => (
                <div key={index} className="history-item">
                  <div className="history-number">
                    <span className={`number ${getNumberColor(game.number)}`}>
                      {game.number}
                    </span>
                  </div>
                  <div className="history-details">
                    <div className="history-time">
                      {formatTime(game.timestamp)}
                    </div>
                    <div className={`history-result ${game.winnings > 0 ? 'win' : 'loss'}`}>
                      {game.winnings > 0 ? `+${game.winnings}` : '0'} Kč
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </>
      )}

      {gameHistory.length === 0 && (
        <div className="no-history">
          <p>🎲 Zatím žádné hry</p>
          <p>Začněte hrát a sledujte své statistiky!</p>
        </div>
      )}
    </div>
  )
}

export default GameStats
