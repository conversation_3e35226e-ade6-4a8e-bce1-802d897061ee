import React from 'react'
import './BettingBoard.css'

const BettingBoard = ({ onPlaceBet, currentBet, bets, disabled }) => {
  const getNumberColor = (number) => {
    if (number === 0) return 'green'
    const redNumbers = [1,3,5,7,9,12,14,16,18,19,21,23,25,27,30,32,34,36]
    return redNumbers.includes(number) ? 'red' : 'black'
  }

  const handleNumberBet = (number) => {
    if (!disabled) {
      onPlaceBet(`number-${number}`, currentBet)
    }
  }

  const handleOutsideBet = (betType) => {
    if (!disabled) {
      onPlaceBet(betType, currentBet)
    }
  }

  const getBetAmount = (betType) => {
    return bets[betType] || 0
  }

  return (
    <div className="betting-board">
      <div className="betting-table">
        {/* Zero sekce */}
        <div className="zero-section">
          <div
            className={`number-cell zero ${getBetAmount('number-0') > 0 ? 'has-bet' : ''}`}
            onClick={() => handleNumberBet(0)}
          >
            <span className="number">0</span>
            {getBetAmount('number-0') > 0 && (
              <span className="bet-chip">{getBetAmount('number-0')}</span>
            )}
          </div>
        </div>

        {/* Hlavní čísla 1-36 v klasickém layoutu */}
        <div className="main-grid">
          <div className="numbers-section">
            {/* Řádek 3: 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36 */}
            <div className="number-row">
              {[3,6,9,12,15,18,21,24,27,30,33,36].map(number => (
                <div
                  key={number}
                  className={`number-cell ${getNumberColor(number)} ${getBetAmount(`number-${number}`) > 0 ? 'has-bet' : ''}`}
                  onClick={() => handleNumberBet(number)}
                >
                  <span className="number">{number}</span>
                  {getBetAmount(`number-${number}`) > 0 && (
                    <span className="bet-chip">{getBetAmount(`number-${number}`)}</span>
                  )}
                </div>
              ))}
            </div>

            {/* Řádek 2: 2, 5, 8, 11, 14, 17, 20, 23, 26, 29, 32, 35 */}
            <div className="number-row">
              {[2,5,8,11,14,17,20,23,26,29,32,35].map(number => (
                <div
                  key={number}
                  className={`number-cell ${getNumberColor(number)} ${getBetAmount(`number-${number}`) > 0 ? 'has-bet' : ''}`}
                  onClick={() => handleNumberBet(number)}
                >
                  <span className="number">{number}</span>
                  {getBetAmount(`number-${number}`) > 0 && (
                    <span className="bet-chip">{getBetAmount(`number-${number}`)}</span>
                  )}
                </div>
              ))}
            </div>

            {/* Řádek 1: 1, 4, 7, 10, 13, 16, 19, 22, 25, 28, 31, 34 */}
            <div className="number-row">
              {[1,4,7,10,13,16,19,22,25,28,31,34].map(number => (
                <div
                  key={number}
                  className={`number-cell ${getNumberColor(number)} ${getBetAmount(`number-${number}`) > 0 ? 'has-bet' : ''}`}
                  onClick={() => handleNumberBet(number)}
                >
                  <span className="number">{number}</span>
                  {getBetAmount(`number-${number}`) > 0 && (
                    <span className="bet-chip">{getBetAmount(`number-${number}`)}</span>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Sloupce 2:1 */}
          <div className="columns-section">
            <div
              className={`column-bet ${getBetAmount('column3') > 0 ? 'has-bet' : ''}`}
              onClick={() => handleOutsideBet('column3')}
            >
              <span>2:1</span>
              {getBetAmount('column3') > 0 && (
                <span className="bet-chip">{getBetAmount('column3')}</span>
              )}
            </div>
            <div
              className={`column-bet ${getBetAmount('column2') > 0 ? 'has-bet' : ''}`}
              onClick={() => handleOutsideBet('column2')}
            >
              <span>2:1</span>
              {getBetAmount('column2') > 0 && (
                <span className="bet-chip">{getBetAmount('column2')}</span>
              )}
            </div>
            <div
              className={`column-bet ${getBetAmount('column1') > 0 ? 'has-bet' : ''}`}
              onClick={() => handleOutsideBet('column1')}
            >
              <span>2:1</span>
              {getBetAmount('column1') > 0 && (
                <span className="bet-chip">{getBetAmount('column1')}</span>
              )}
            </div>
          </div>
        </div>

        {/* Tucty */}
        <div className="dozens-section">
          <div
            className={`dozen-bet ${getBetAmount('dozen1') > 0 ? 'has-bet' : ''}`}
            onClick={() => handleOutsideBet('dozen1')}
          >
            <span>1st 12</span>
            {getBetAmount('dozen1') > 0 && (
              <span className="bet-chip">{getBetAmount('dozen1')}</span>
            )}
          </div>
          <div
            className={`dozen-bet ${getBetAmount('dozen2') > 0 ? 'has-bet' : ''}`}
            onClick={() => handleOutsideBet('dozen2')}
          >
            <span>2nd 12</span>
            {getBetAmount('dozen2') > 0 && (
              <span className="bet-chip">{getBetAmount('dozen2')}</span>
            )}
          </div>
          <div
            className={`dozen-bet ${getBetAmount('dozen3') > 0 ? 'has-bet' : ''}`}
            onClick={() => handleOutsideBet('dozen3')}
          >
            <span>3rd 12</span>
            {getBetAmount('dozen3') > 0 && (
              <span className="bet-chip">{getBetAmount('dozen3')}</span>
            )}
          </div>
        </div>

        {/* Vnější sázky */}
        <div className="outside-section">
          <div
            className={`outside-bet ${getBetAmount('low') > 0 ? 'has-bet' : ''}`}
            onClick={() => handleOutsideBet('low')}
          >
            <span>1-18</span>
            {getBetAmount('low') > 0 && (
              <span className="bet-chip">{getBetAmount('low')}</span>
            )}
          </div>
          <div
            className={`outside-bet even-odd ${getBetAmount('even') > 0 ? 'has-bet' : ''}`}
            onClick={() => handleOutsideBet('even')}
          >
            <span>EVEN</span>
            {getBetAmount('even') > 0 && (
              <span className="bet-chip">{getBetAmount('even')}</span>
            )}
          </div>
          <div
            className={`outside-bet red-black red ${getBetAmount('red') > 0 ? 'has-bet' : ''}`}
            onClick={() => handleOutsideBet('red')}
          >
            <span>♦</span>
            {getBetAmount('red') > 0 && (
              <span className="bet-chip">{getBetAmount('red')}</span>
            )}
          </div>
          <div
            className={`outside-bet red-black black ${getBetAmount('black') > 0 ? 'has-bet' : ''}`}
            onClick={() => handleOutsideBet('black')}
          >
            <span>♠</span>
            {getBetAmount('black') > 0 && (
              <span className="bet-chip">{getBetAmount('black')}</span>
            )}
          </div>
          <div
            className={`outside-bet even-odd ${getBetAmount('odd') > 0 ? 'has-bet' : ''}`}
            onClick={() => handleOutsideBet('odd')}
          >
            <span>ODD</span>
            {getBetAmount('odd') > 0 && (
              <span className="bet-chip">{getBetAmount('odd')}</span>
            )}
          </div>
          <div
            className={`outside-bet ${getBetAmount('high') > 0 ? 'has-bet' : ''}`}
            onClick={() => handleOutsideBet('high')}
          >
            <span>19-36</span>
            {getBetAmount('high') > 0 && (
              <span className="bet-chip">{getBetAmount('high')}</span>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default BettingBoard
