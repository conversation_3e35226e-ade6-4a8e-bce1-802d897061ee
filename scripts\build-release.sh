#!/bin/bash

# MindFlow Mobile App - Release Build Script
# Automatizuje build proces pro Android a iOS

set -e  # Exit on any error

echo "🧠 MindFlow - Release Build Script"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="MindFlow"
ANDROID_PACKAGE="com.mindflow.app"
IOS_BUNDLE_ID="com.mindflow.app"
VERSION_CODE=$(date +%Y%m%d%H)
VERSION_NAME="1.0.0"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_dependencies() {
    log_info "Checking dependencies..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed"
        exit 1
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        log_error "npm is not installed"
        exit 1
    fi
    
    # Check React Native CLI
    if ! command -v npx react-native &> /dev/null; then
        log_error "React Native CLI is not available"
        exit 1
    fi
    
    log_success "All dependencies are available"
}

install_dependencies() {
    log_info "Installing npm dependencies..."
    npm ci
    log_success "npm dependencies installed"
}

clean_project() {
    log_info "Cleaning project..."
    
    # Clean npm cache
    npm cache clean --force
    
    # Clean React Native cache
    npx react-native clean-project-auto
    
    # Clean Android
    if [ -d "android" ]; then
        cd android
        ./gradlew clean
        cd ..
    fi
    
    # Clean iOS
    if [ -d "ios" ]; then
        cd ios
        rm -rf build/
        rm -rf DerivedData/
        if command -v pod &> /dev/null; then
            pod cache clean --all
        fi
        cd ..
    fi
    
    log_success "Project cleaned"
}

update_version() {
    log_info "Updating version to $VERSION_NAME ($VERSION_CODE)..."
    
    # Update package.json
    npm version $VERSION_NAME --no-git-tag-version
    
    # Update Android version
    if [ -d "android" ]; then
        sed -i.bak "s/versionCode [0-9]*/versionCode $VERSION_CODE/" android/app/build.gradle
        sed -i.bak "s/versionName \".*\"/versionName \"$VERSION_NAME\"/" android/app/build.gradle
        rm android/app/build.gradle.bak
    fi
    
    # Update iOS version (if Info.plist exists)
    if [ -f "ios/$APP_NAME/Info.plist" ]; then
        /usr/libexec/PlistBuddy -c "Set :CFBundleShortVersionString $VERSION_NAME" "ios/$APP_NAME/Info.plist"
        /usr/libexec/PlistBuddy -c "Set :CFBundleVersion $VERSION_CODE" "ios/$APP_NAME/Info.plist"
    fi
    
    log_success "Version updated"
}

build_android() {
    log_info "Building Android release..."
    
    if [ ! -d "android" ]; then
        log_error "Android directory not found"
        return 1
    fi
    
    cd android
    
    # Build release APK
    log_info "Building release APK..."
    ./gradlew assembleRelease
    
    # Build release AAB (for Google Play)
    log_info "Building release AAB..."
    ./gradlew bundleRelease
    
    cd ..
    
    # Copy outputs to release directory
    mkdir -p release/android
    
    if [ -f "android/app/build/outputs/apk/release/app-release.apk" ]; then
        cp android/app/build/outputs/apk/release/app-release.apk release/android/
        log_success "APK built: release/android/app-release.apk"
    fi
    
    if [ -f "android/app/build/outputs/bundle/release/app-release.aab" ]; then
        cp android/app/build/outputs/bundle/release/app-release.aab release/android/
        log_success "AAB built: release/android/app-release.aab"
    fi
}

build_ios() {
    log_info "Building iOS release..."
    
    if [ ! -d "ios" ]; then
        log_error "iOS directory not found"
        return 1
    fi
    
    # Check if we're on macOS
    if [[ "$OSTYPE" != "darwin"* ]]; then
        log_warning "iOS build is only supported on macOS"
        return 0
    fi
    
    # Check if Xcode is installed
    if ! command -v xcodebuild &> /dev/null; then
        log_error "Xcode is not installed"
        return 1
    fi
    
    cd ios
    
    # Install pods
    if command -v pod &> /dev/null; then
        log_info "Installing CocoaPods dependencies..."
        pod install
    fi
    
    # Build archive
    log_info "Building iOS archive..."
    xcodebuild -workspace $APP_NAME.xcworkspace \
               -scheme $APP_NAME \
               -configuration Release \
               -archivePath build/$APP_NAME.xcarchive \
               archive
    
    # Export IPA
    log_info "Exporting IPA..."
    xcodebuild -exportArchive \
               -archivePath build/$APP_NAME.xcarchive \
               -exportPath build/ \
               -exportOptionsPlist ExportOptions.plist
    
    cd ..
    
    # Copy outputs to release directory
    mkdir -p release/ios
    
    if [ -f "ios/build/$APP_NAME.ipa" ]; then
        cp ios/build/$APP_NAME.ipa release/ios/
        log_success "IPA built: release/ios/$APP_NAME.ipa"
    fi
}

generate_release_notes() {
    log_info "Generating release notes..."
    
    cat > release/RELEASE_NOTES.md << EOF
# MindFlow v$VERSION_NAME - Release Notes

## 📱 Build Information
- **Version:** $VERSION_NAME
- **Build:** $VERSION_CODE
- **Date:** $(date '+%Y-%m-%d %H:%M:%S')
- **Platform:** Android & iOS

## 🚀 What's New
- Initial release of MindFlow
- Mood tracking with advanced analytics
- Guided meditation sessions
- Breathing exercises
- Sleep stories
- Community support
- Smart monetization with targeted ads

## 📊 Technical Details
- **React Native:** 0.72.6
- **Target SDK:** Android 34, iOS 17
- **Minimum SDK:** Android 21, iOS 12
- **Bundle Size:** ~50MB

## 🔧 Installation
### Android:
1. Download app-release.apk or app-release.aab
2. Install via ADB or upload to Google Play Console

### iOS:
1. Download $APP_NAME.ipa
2. Install via Xcode or upload to App Store Connect

## 💰 Monetization
- Freemium model with premium subscriptions
- Targeted advertising integration
- Affiliate marketing partnerships

## 🎯 Next Steps
1. Upload to app stores
2. Set up analytics dashboard
3. Configure ad networks
4. Launch marketing campaign

---
Built with ❤️ by the MindFlow team
EOF

    log_success "Release notes generated: release/RELEASE_NOTES.md"
}

create_release_package() {
    log_info "Creating release package..."
    
    # Create release directory structure
    mkdir -p release/{android,ios,docs}
    
    # Copy important files
    cp MINDFLOW_README.md release/docs/
    cp package.json release/docs/
    
    # Create checksums
    if [ -f "release/android/app-release.apk" ]; then
        cd release/android
        sha256sum app-release.apk > app-release.apk.sha256
        cd ../..
    fi
    
    if [ -f "release/android/app-release.aab" ]; then
        cd release/android
        sha256sum app-release.aab > app-release.aab.sha256
        cd ../..
    fi
    
    # Create zip archive
    cd release
    zip -r "../mindflow-v$VERSION_NAME-build$VERSION_CODE.zip" .
    cd ..
    
    log_success "Release package created: mindflow-v$VERSION_NAME-build$VERSION_CODE.zip"
}

print_summary() {
    echo ""
    echo "🎉 Build Summary"
    echo "================"
    echo "App Name: $APP_NAME"
    echo "Version: $VERSION_NAME ($VERSION_CODE)"
    echo "Date: $(date '+%Y-%m-%d %H:%M:%S')"
    echo ""
    
    if [ -f "release/android/app-release.apk" ]; then
        APK_SIZE=$(du -h release/android/app-release.apk | cut -f1)
        echo "✅ Android APK: $APK_SIZE"
    fi
    
    if [ -f "release/android/app-release.aab" ]; then
        AAB_SIZE=$(du -h release/android/app-release.aab | cut -f1)
        echo "✅ Android AAB: $AAB_SIZE"
    fi
    
    if [ -f "release/ios/$APP_NAME.ipa" ]; then
        IPA_SIZE=$(du -h release/ios/$APP_NAME.ipa | cut -f1)
        echo "✅ iOS IPA: $IPA_SIZE"
    fi
    
    echo ""
    echo "📦 Release package: mindflow-v$VERSION_NAME-build$VERSION_CODE.zip"
    echo ""
    echo "🚀 Ready for deployment!"
    echo ""
    echo "Next steps:"
    echo "1. Test the builds on real devices"
    echo "2. Upload to Google Play Console (AAB)"
    echo "3. Upload to App Store Connect (IPA)"
    echo "4. Configure app store listings"
    echo "5. Set up analytics and ad networks"
}

# Main execution
main() {
    echo "Starting build process..."
    
    # Parse command line arguments
    PLATFORM="all"
    CLEAN=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --platform)
                PLATFORM="$2"
                shift 2
                ;;
            --clean)
                CLEAN=true
                shift
                ;;
            --version)
                VERSION_NAME="$2"
                shift 2
                ;;
            --help)
                echo "Usage: $0 [options]"
                echo "Options:"
                echo "  --platform [android|ios|all]  Build specific platform (default: all)"
                echo "  --clean                        Clean project before build"
                echo "  --version VERSION              Set version name"
                echo "  --help                         Show this help"
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Execute build steps
    check_dependencies
    install_dependencies
    
    if [ "$CLEAN" = true ]; then
        clean_project
    fi
    
    update_version
    
    # Build platforms
    case $PLATFORM in
        android)
            build_android
            ;;
        ios)
            build_ios
            ;;
        all)
            build_android
            build_ios
            ;;
        *)
            log_error "Invalid platform: $PLATFORM"
            exit 1
            ;;
    esac
    
    generate_release_notes
    create_release_package
    print_summary
    
    log_success "Build completed successfully! 🎉"
}

# Run main function
main "$@"
