{"name": "mindflow-mobile", "version": "1.0.0", "description": "MindFlow - Mental Wellness App for millions of people worldwide", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint .", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace MindFlow.xcworkspace -scheme MindFlow -configuration Release archive", "bundle:android": "npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle", "clean": "react-native clean-project-auto"}, "dependencies": {"react": "18.2.0", "react-native": "0.72.6", "react-native-vector-icons": "^10.0.0", "react-native-linear-gradient": "^2.8.3", "react-native-blur": "^4.3.2", "react-native-gesture-handler": "^2.13.4", "react-native-reanimated": "^3.5.4", "react-native-safe-area-context": "^4.7.4", "react-native-screens": "^3.27.0", "@react-navigation/native": "^6.1.9", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/stack": "^6.3.20", "react-native-async-storage": "^1.19.5", "react-native-push-notification": "^8.1.1", "react-native-sound": "^0.11.2", "react-native-progress": "^5.0.1", "react-native-chart-kit": "^6.12.0", "react-native-svg": "^13.14.0", "react-native-admob-native-ads": "^0.6.0", "react-native-google-mobile-ads": "^12.9.0", "react-native-iap": "^12.10.7", "react-native-haptic-feedback": "^2.2.0", "react-native-device-info": "^10.11.0", "react-native-permissions": "^3.10.1", "lottie-react-native": "^6.4.1", "react-native-modal": "^13.0.1", "react-native-slider": "^2.4.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}, "keywords": ["mental-health", "wellness", "meditation", "mindfulness", "mood-tracker", "react-native", "mobile-app"], "author": "MindFlow Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/mindflow/mindflow-mobile.git"}, "bugs": {"url": "https://github.com/mindflow/mindflow-mobile/issues"}, "homepage": "https://mindflow.app"}