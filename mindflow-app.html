<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 MindFlow - Mentální Wellness App</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .phone-container {
            max-width: 375px;
            margin: 20px auto;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            position: relative;
        }

        .phone-screen {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 32px;
            height: 812px;
            overflow: hidden;
            position: relative;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 25px 10px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }

        .notch {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 150px;
            height: 30px;
            background: #000;
            border-radius: 0 0 20px 20px;
            z-index: 10;
        }

        .app-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: calc(100% - 50px);
            border-radius: 25px 25px 0 0;
            position: relative;
            overflow: hidden;
        }

        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .shape {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 6s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .content {
            position: relative;
            z-index: 2;
            padding: 30px 25px;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo {
            font-size: 48px;
            margin-bottom: 10px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .app-title {
            color: white;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 5px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .app-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            font-weight: 400;
        }

        .mood-tracker {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .mood-title {
            color: white;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            text-align: center;
        }

        .mood-options {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .mood-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            font-size: 24px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .mood-btn:hover {
            transform: scale(1.1);
            background: rgba(255, 255, 255, 0.3);
        }

        .mood-btn.selected {
            background: rgba(255, 255, 255, 0.4);
            transform: scale(1.15);
            box-shadow: 0 5px 20px rgba(255, 255, 255, 0.3);
        }

        .features-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.25);
        }

        .feature-icon {
            font-size: 32px;
            margin-bottom: 10px;
            display: block;
        }

        .feature-title {
            color: white;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .feature-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
        }

        .ad-banner {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .ad-banner:hover {
            transform: scale(1.02);
        }

        .ad-banner::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: shine 3s infinite;
        }

        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .ad-text {
            color: #333;
            font-size: 14px;
            font-weight: 600;
            position: relative;
            z-index: 2;
        }

        .ad-label {
            position: absolute;
            top: 5px;
            right: 10px;
            background: rgba(0, 0, 0, 0.2);
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 8px;
        }

        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            padding: 15px 0;
        }

        .nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .nav-item {
            text-align: center;
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-item.active {
            color: white;
            transform: scale(1.1);
        }

        .nav-icon {
            font-size: 24px;
            margin-bottom: 5px;
            display: block;
        }

        .nav-label {
            font-size: 12px;
            font-weight: 500;
        }

        .stats-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .stats-title {
            color: white;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            text-align: center;
        }

        .stat-item {
            color: white;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            display: block;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.8;
        }

        .floating-action {
            position: absolute;
            bottom: 100px;
            right: 25px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            border-radius: 50%;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.4);
            transition: all 0.3s ease;
            z-index: 10;
        }

        .floating-action:hover {
            transform: scale(1.1);
            box-shadow: 0 15px 40px rgba(255, 107, 107, 0.6);
        }

        .notification {
            position: absolute;
            top: 80px;
            left: 25px;
            right: 25px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 15px;
            transform: translateY(-100px);
            opacity: 0;
            transition: all 0.5s ease;
            z-index: 20;
        }

        .notification.show {
            transform: translateY(0);
            opacity: 1;
        }

        .notification-text {
            color: #333;
            font-size: 14px;
            font-weight: 500;
        }

        .premium-badge {
            position: absolute;
            top: 15px;
            right: 25px;
            background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
            color: #333;
            font-size: 12px;
            font-weight: 700;
            padding: 5px 10px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="notch"></div>
        <div class="phone-screen">
            <div class="status-bar">
                <span>9:41</span>
                <span>🔋 100%</span>
            </div>
            
            <div class="app-container">
                <div class="floating-shapes">
                    <div class="shape"></div>
                    <div class="shape"></div>
                    <div class="shape"></div>
                </div>
                
                <div class="premium-badge">✨ PRO</div>
                
                <div class="content">
                    <div class="header">
                        <div class="logo">🧠</div>
                        <h1 class="app-title">MindFlow</h1>
                        <p class="app-subtitle">Tvoje cesta k mentální pohodě</p>
                    </div>
                    
                    <div class="mood-tracker">
                        <h3 class="mood-title">Jak se dnes cítíš?</h3>
                        <div class="mood-options">
                            <button class="mood-btn" onclick="selectMood(this, 'amazing')">🤩</button>
                            <button class="mood-btn" onclick="selectMood(this, 'good')">😊</button>
                            <button class="mood-btn" onclick="selectMood(this, 'okay')">😐</button>
                            <button class="mood-btn" onclick="selectMood(this, 'bad')">😔</button>
                            <button class="mood-btn" onclick="selectMood(this, 'terrible')">😢</button>
                        </div>
                    </div>
                    
                    <div class="ad-banner" onclick="showNotification('Děkujeme za podporu! 💝')">
                        <div class="ad-label">Reklama</div>
                        <div class="ad-text">🎧 Spotify Premium - 3 měsíce zdarma!</div>
                    </div>
                    
                    <div class="features-grid">
                        <div class="feature-card" onclick="openFeature('meditation')">
                            <span class="feature-icon">🧘‍♀️</span>
                            <div class="feature-title">Meditace</div>
                            <div class="feature-subtitle">Zklidni mysl</div>
                        </div>
                        <div class="feature-card" onclick="openFeature('breathing')">
                            <span class="feature-icon">🫁</span>
                            <div class="feature-title">Dýchání</div>
                            <div class="feature-subtitle">Relaxační techniky</div>
                        </div>
                        <div class="feature-card" onclick="openFeature('journal')">
                            <span class="feature-icon">📝</span>
                            <div class="feature-title">Deník</div>
                            <div class="feature-subtitle">Zapiš si myšlenky</div>
                        </div>
                        <div class="feature-card" onclick="openFeature('sleep')">
                            <span class="feature-icon">😴</span>
                            <div class="feature-title">Spánek</div>
                            <div class="feature-subtitle">Lepší odpočinek</div>
                        </div>
                    </div>
                    
                    <div class="stats-card">
                        <h3 class="stats-title">Tvůj pokrok tento týden</h3>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <span class="stat-number" id="meditation-count">12</span>
                                <span class="stat-label">Meditací</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number" id="mood-score">8.2</span>
                                <span class="stat-label">Průměrná nálada</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number" id="streak-days">7</span>
                                <span class="stat-label">Dní v řadě</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <button class="floating-action" onclick="quickMeditation()">
                    ▶️
                </button>
                
                <div class="bottom-nav">
                    <div class="nav-items">
                        <div class="nav-item active" onclick="switchTab(this, 'home')">
                            <span class="nav-icon">🏠</span>
                            <span class="nav-label">Domů</span>
                        </div>
                        <div class="nav-item" onclick="switchTab(this, 'progress')">
                            <span class="nav-icon">📊</span>
                            <span class="nav-label">Pokrok</span>
                        </div>
                        <div class="nav-item" onclick="switchTab(this, 'community')">
                            <span class="nav-icon">👥</span>
                            <span class="nav-label">Komunita</span>
                        </div>
                        <div class="nav-item" onclick="switchTab(this, 'profile')">
                            <span class="nav-icon">👤</span>
                            <span class="nav-label">Profil</span>
                        </div>
                    </div>
                </div>
                
                <div class="notification" id="notification">
                    <div class="notification-text" id="notification-text"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="mindflow-app.js"></script>
</body>
</html>
