#!/usr/bin/env python3
"""
CS2 Network Optimizer
Optimalizuje Counter-Strike 2 pro různé typy připojení včetně satelitního internetu
Autor: Augment Agent
"""

import json
import subprocess
import winreg
import ctypes
import time
import threading
from pathlib import Path
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext

class CS2NetworkOptimizer:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("CS2 Network Optimizer v1.0")
        self.root.geometry("800x600")
        self.root.resizable(True, True)

        # Konfigurace pro různé typy připojení
        self.connection_profiles = {
            "satellite": {
                "name": "Satelitní (Starnet, Starlink)",
                "rate": "160000",
                "cl_updaterate": "64",
                "cl_cmdrate": "64",
                "cl_interp": "0.015625",
                "cl_interp_ratio": "2",
                "net_maxroutable": "1200",
                "fps_max": "165",
                "description": "Optimalizováno pro satelitní připojení s vy<PERSON><PERSON> latenc<PERSON>"
            },
            "fiber": {
                "name": "Optika/Kabel",
                "rate": "786432",
                "cl_updaterate": "128",
                "cl_cmdrate": "128",
                "cl_interp": "0",
                "cl_interp_ratio": "1",
                "net_maxroutable": "1460",
                "fps_max": "240",
                "description": "Optimalizováno pro rychlé stabilní připojení"
            },
            "wifi": {
                "name": "WiFi",
                "rate": "196608",
                "cl_updaterate": "64",
                "cl_cmdrate": "64",
                "cl_interp": "0.007813",
                "cl_interp_ratio": "1",
                "net_maxroutable": "1200",
                "fps_max": "144",
                "description": "Optimalizováno pro WiFi s možným jitterem"
            },
            "mobile": {
                "name": "Mobilní data",
                "rate": "128000",
                "cl_updaterate": "64",
                "cl_cmdrate": "64",
                "cl_interp": "0.03125",
                "cl_interp_ratio": "2",
                "net_maxroutable": "1200",
                "fps_max": "120",
                "description": "Optimalizováno pro mobilní připojení"
            }
        }

        self.setup_ui()
        self.load_settings()

    def setup_ui(self):
        # Hlavní notebook
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill="both", expand=True, padx=10, pady=10)

        # Tab 1: Profily připojení
        self.profile_frame = ttk.Frame(notebook)
        notebook.add(self.profile_frame, text="Profily připojení")
        self.setup_profile_tab()

        # Tab 2: Pokročilé nastavení
        self.advanced_frame = ttk.Frame(notebook)
        notebook.add(self.advanced_frame, text="Pokročilé nastavení")
        self.setup_advanced_tab()

        # Tab 3: Diagnostika
        self.diagnostic_frame = ttk.Frame(notebook)
        notebook.add(self.diagnostic_frame, text="Diagnostika")
        self.setup_diagnostic_tab()

        # Tab 4: Windows optimalizace
        self.windows_frame = ttk.Frame(notebook)
        notebook.add(self.windows_frame, text="Windows optimalizace")
        self.setup_windows_tab()

    def setup_profile_tab(self):
        # Výběr profilu
        ttk.Label(self.profile_frame, text="Vyberte typ vašeho připojení:", font=("Arial", 12, "bold")).pack(pady=10)

        self.selected_profile = tk.StringVar(value="satellite")

        for key, profile in self.connection_profiles.items():
            frame = ttk.LabelFrame(self.profile_frame, text=profile["name"], padding=10)
            frame.pack(fill="x", padx=20, pady=5)

            ttk.Radiobutton(frame, text=profile["description"],
                          variable=self.selected_profile, value=key).pack(anchor="w")

            # Zobrazení nastavení
            settings_text = f"Rate: {profile['rate']}, Update/Cmd: {profile['cl_updaterate']}/{profile['cl_cmdrate']}, FPS: {profile['fps_max']}"
            ttk.Label(frame, text=settings_text, font=("Arial", 8)).pack(anchor="w")

        # Tlačítka
        button_frame = ttk.Frame(self.profile_frame)
        button_frame.pack(pady=20)

        ttk.Button(button_frame, text="Aplikovat profil",
                  command=self.apply_profile, style="Accent.TButton").pack(side="left", padx=5)
        ttk.Button(button_frame, text="Test připojení",
                  command=self.test_connection).pack(side="left", padx=5)

    def setup_advanced_tab(self):
        # Pokročilé CS2 nastavení
        ttk.Label(self.advanced_frame, text="Pokročilé CS2 nastavení", font=("Arial", 12, "bold")).pack(pady=10)

        # Scrollable frame pro nastavení
        canvas = tk.Canvas(self.advanced_frame)
        scrollbar = ttk.Scrollbar(self.advanced_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda _: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # CS2 příkazy
        self.cs2_commands = {
            "rate": tk.StringVar(value="160000"),
            "cl_updaterate": tk.StringVar(value="64"),
            "cl_cmdrate": tk.StringVar(value="64"),
            "cl_interp": tk.StringVar(value="0"),
            "cl_interp_ratio": tk.StringVar(value="1"),
            "fps_max": tk.StringVar(value="165"),
            "net_maxroutable": tk.StringVar(value="1200"),
            "cl_predict": tk.StringVar(value="1"),
            "cl_lagcompensation": tk.StringVar(value="1"),
            "net_splitpacket_maxrate": tk.StringVar(value="50000")
        }

        for cmd, var in self.cs2_commands.items():
            frame = ttk.Frame(scrollable_frame)
            frame.pack(fill="x", padx=10, pady=2)
            ttk.Label(frame, text=f"{cmd}:", width=20).pack(side="left")
            ttk.Entry(frame, textvariable=var, width=15).pack(side="left", padx=5)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Tlačítka
        ttk.Button(self.advanced_frame, text="Generovat autoexec.cfg",
                  command=self.generate_autoexec).pack(pady=10)

    def setup_diagnostic_tab(self):
        ttk.Label(self.diagnostic_frame, text="Diagnostika připojení", font=("Arial", 12, "bold")).pack(pady=10)

        # Výsledky testů
        self.diagnostic_text = scrolledtext.ScrolledText(self.diagnostic_frame, height=20, width=80)
        self.diagnostic_text.pack(fill="both", expand=True, padx=10, pady=10)

        # Tlačítka pro testy
        test_frame = ttk.Frame(self.diagnostic_frame)
        test_frame.pack(pady=10)

        ttk.Button(test_frame, text="Detekovat nastavení", command=self.detect_current_settings).pack(side="left", padx=5)
        ttk.Button(test_frame, text="Ping test", command=self.ping_test).pack(side="left", padx=5)
        ttk.Button(test_frame, text="Test rychlosti", command=self.speed_test).pack(side="left", padx=5)

        # Druhý řádek tlačítek
        test_frame2 = ttk.Frame(self.diagnostic_frame)
        test_frame2.pack(pady=5)

        ttk.Button(test_frame2, text="🚀 SMART OPTIMALIZACE",
                  command=self.apply_smart_optimizations,
                  style="Accent.TButton").pack(side="left", padx=5)
        ttk.Button(test_frame2, text="💾 Vytvořit backup", command=self.create_backup).pack(side="left", padx=5)
        ttk.Button(test_frame2, text="🔄 Obnovit backup", command=self.restore_backup).pack(side="left", padx=5)

    def setup_windows_tab(self):
        ttk.Label(self.windows_frame, text="Windows síťové optimalizace", font=("Arial", 12, "bold")).pack(pady=10)

        # Checkboxy pro optimalizace
        self.windows_optimizations = {
            "tcp_window_scaling": tk.BooleanVar(value=True),
            "tcp_chimney": tk.BooleanVar(value=True),
            "nagle_disable": tk.BooleanVar(value=True),
            "interrupt_moderation": tk.BooleanVar(value=True),
            "power_management": tk.BooleanVar(value=True),
            "game_mode": tk.BooleanVar(value=True),
            "dns_optimization": tk.BooleanVar(value=True)
        }

        optimizations_desc = {
            "tcp_window_scaling": "TCP Window Scaling (pro satelitní připojení)",
            "tcp_chimney": "TCP Chimney Offload",
            "nagle_disable": "Vypnout Nagle algoritmus",
            "interrupt_moderation": "Optimalizovat síťovou kartu",
            "power_management": "Vypnout úsporu energie USB/síť",
            "game_mode": "Zapnout Windows Game Mode",
            "dns_optimization": "Optimalizovat DNS (*******, *******)"
        }

        for key, var in self.windows_optimizations.items():
            ttk.Checkbutton(self.windows_frame, text=optimizations_desc[key],
                          variable=var).pack(anchor="w", padx=20, pady=2)

        ttk.Button(self.windows_frame, text="Aplikovat Windows optimalizace",
                  command=self.apply_windows_optimizations).pack(pady=20)

        # Varování
        warning_text = "⚠️ Některé optimalizace vyžadují restart počítače\n⚠️ Spusťte aplikaci jako administrátor pro plnou funkcionalitu"
        ttk.Label(self.windows_frame, text=warning_text, foreground="red").pack(pady=10)

    def apply_profile(self):
        """Aplikuje vybraný profil připojení"""
        profile = self.connection_profiles[self.selected_profile.get()]

        # Aktualizuje pokročilé nastavení podle profilu
        for cmd, value in profile.items():
            if cmd in self.cs2_commands:
                self.cs2_commands[cmd].set(value)

        # Generuje autoexec.cfg
        self.generate_autoexec()
        messagebox.showinfo("Úspěch", f"Profil '{profile['name']}' byl aplikován!")

    def generate_autoexec(self):
        """Generuje autoexec.cfg soubor pro CS2"""
        try:
            # Najde CS2 složku
            cs2_path = self.find_cs2_path()
            if not cs2_path:
                messagebox.showerror("Chyba", "CS2 instalace nebyla nalezena!")
                return

            # Vytvoří autoexec.cfg obsah
            config_content = self.create_autoexec_content()

            # Uloží soubor
            autoexec_path = cs2_path / "game" / "csgo" / "cfg" / "autoexec.cfg"
            autoexec_path.parent.mkdir(parents=True, exist_ok=True)

            with open(autoexec_path, 'w', encoding='utf-8') as f:
                f.write(config_content)

            messagebox.showinfo("Úspěch", f"autoexec.cfg byl vytvořen:\n{autoexec_path}")

        except Exception as e:
            messagebox.showerror("Chyba", f"Nepodařilo se vytvořit autoexec.cfg: {str(e)}")

    def create_autoexec_content(self):
        """Vytvoří obsah autoexec.cfg"""
        content = [
            "// CS2 Network Optimizer - Automaticky generovaný config",
            "// Profil: " + self.connection_profiles[self.selected_profile.get()]["name"],
            "// Generováno: " + time.strftime("%Y-%m-%d %H:%M:%S"),
            "",
            "// Síťové nastavení",
        ]

        for cmd, var in self.cs2_commands.items():
            content.append(f"{cmd} {var.get()}")

        content.extend([
            "",
            "// Další optimalizace",
            "cl_predict 1",
            "cl_lagcompensation 1",
            "net_graph 1",
            "cl_showfps 1",
            "",
            "// Satelitní specifické optimalizace",
            "net_maxcleartime 0.001",
            "cl_timeout 30",
            "",
            "echo 'CS2 Network Optimizer config loaded!'"
        ])

        return "\n".join(content)

    def find_cs2_path(self):
        """Najde cestu k CS2 instalaci"""
        possible_paths = [
            Path("C:/Program Files (x86)/Steam/steamapps/common/Counter-Strike Global Offensive"),
            Path("C:/Program Files/Steam/steamapps/common/Counter-Strike Global Offensive"),
            Path("D:/Steam/steamapps/common/Counter-Strike Global Offensive"),
            Path("E:/Steam/steamapps/common/Counter-Strike Global Offensive")
        ]

        for path in possible_paths:
            if path.exists():
                return path

        # Pokusí se najít přes registry
        try:
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                              r"SOFTWARE\WOW6432Node\Valve\Steam") as key:
                steam_path = winreg.QueryValueEx(key, "InstallPath")[0]
                cs2_path = Path(steam_path) / "steamapps" / "common" / "Counter-Strike Global Offensive"
                if cs2_path.exists():
                    return cs2_path
        except:
            pass

        return None

    def test_connection(self):
        """Spustí test připojení"""
        self.diagnostic_text.delete(1.0, tk.END)
        self.diagnostic_text.insert(tk.END, "Spouštím test připojení...\n")
        self.root.update()

        # Spustí testy v separátním vlákně
        threading.Thread(target=self._run_connection_tests, daemon=True).start()

    def _run_connection_tests(self):
        """Spustí testy připojení v separátním vlákně"""
        try:
            # Ping test
            self.diagnostic_text.insert(tk.END, "\n=== PING TEST ===\n")
            self.root.update()

            ping_results = self._ping_test_internal(["*******", "*******", "************"])
            for host, result in ping_results.items():
                self.diagnostic_text.insert(tk.END, f"{host}: {result}\n")
                self.root.update()

            # Speed test (zjednodušený)
            self.diagnostic_text.insert(tk.END, "\n=== RYCHLOST ===\n")
            self.diagnostic_text.insert(tk.END, "Pro přesný speed test použijte fast.com nebo speedtest.net\n")
            self.root.update()

            # Doporučení na základě profilu
            profile = self.connection_profiles[self.selected_profile.get()]
            self.diagnostic_text.insert(tk.END, f"\n=== DOPORUČENÍ PRO {profile['name'].upper()} ===\n")
            self.diagnostic_text.insert(tk.END, f"Rate: {profile['rate']}\n")
            self.diagnostic_text.insert(tk.END, f"Update/Cmd rate: {profile['cl_updaterate']}/{profile['cl_cmdrate']}\n")
            self.diagnostic_text.insert(tk.END, f"Max FPS: {profile['fps_max']}\n")
            self.root.update()

        except Exception as e:
            self.diagnostic_text.insert(tk.END, f"\nChyba při testování: {str(e)}\n")
            self.root.update()

    def _ping_test_internal(self, hosts):
        """Interní ping test"""
        results = {}
        for host in hosts:
            try:
                result = subprocess.run(['ping', '-n', '4', host],
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    # Parsuje výsledek pingu
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if 'Average' in line or 'Průměr' in line:
                            results[host] = line.strip()
                            break
                    else:
                        results[host] = "OK"
                else:
                    results[host] = "FAILED"
            except:
                results[host] = "TIMEOUT"
        return results

    def create_backup(self):
        """Vytvoří backup současného nastavení"""
        try:
            backup_data = {
                "timestamp": time.strftime("%Y-%m-%d_%H-%M-%S"),
                "registry_backup": {},
                "network_settings": {},
                "dns_settings": {}
            }

            # Backup registry nastavení
            self._backup_registry_settings(backup_data)

            # Backup síťových nastavení
            self._backup_network_settings(backup_data)

            # Uloží backup
            backup_file = f"cs2_backup_{backup_data['timestamp']}.json"
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, indent=2, ensure_ascii=False)

            messagebox.showinfo("Backup vytvořen", f"Backup uložen jako: {backup_file}")
            return backup_file

        except Exception as e:
            messagebox.showerror("Chyba", f"Nepodařilo se vytvořit backup: {str(e)}")
            return None

    def _backup_registry_settings(self, backup_data):
        """Zálohuje registry nastavení"""
        registry_paths = [
            (winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters"),
            (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\GameBar"),
            (winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Services\USB")
        ]

        for hkey, path in registry_paths:
            try:
                with winreg.OpenKey(hkey, path) as key:
                    backup_data["registry_backup"][path] = {}
                    try:
                        i = 0
                        while True:
                            name, value, type = winreg.EnumValue(key, i)
                            backup_data["registry_backup"][path][name] = {
                                "value": value,
                                "type": type
                            }
                            i += 1
                    except WindowsError:
                        pass
            except:
                continue

    def _backup_network_settings(self, backup_data):
        """Zálohuje síťová nastavení"""
        try:
            # Backup DNS nastavení
            result = subprocess.run(['netsh', 'interface', 'ip', 'show', 'dns'],
                                  capture_output=True, text=True)
            backup_data["dns_settings"] = result.stdout

            # Backup TCP nastavení
            result = subprocess.run(['netsh', 'int', 'tcp', 'show', 'global'],
                                  capture_output=True, text=True)
            backup_data["network_settings"]["tcp_global"] = result.stdout

        except Exception as e:
            print(f"Chyba při zálohování síťových nastavení: {e}")

    def restore_backup(self, backup_file=None):
        """Obnoví nastavení z backupu"""
        if not backup_file:
            from tkinter import filedialog
            backup_file = filedialog.askopenfilename(
                title="Vyberte backup soubor",
                filetypes=[("JSON files", "*.json")]
            )

        if not backup_file:
            return

        try:
            with open(backup_file, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)

            # Obnoví registry nastavení
            self._restore_registry_settings(backup_data)

            messagebox.showinfo("Úspěch", "Backup byl obnoven! Doporučujeme restart počítače.")

        except Exception as e:
            messagebox.showerror("Chyba", f"Nepodařilo se obnovit backup: {str(e)}")

    def _restore_registry_settings(self, backup_data):
        """Obnoví registry nastavení"""
        for path, values in backup_data.get("registry_backup", {}).items():
            try:
                if "HKEY_CURRENT_USER" in path:
                    hkey = winreg.HKEY_CURRENT_USER
                    reg_path = path.replace("HKEY_CURRENT_USER\\", "")
                else:
                    hkey = winreg.HKEY_LOCAL_MACHINE
                    reg_path = path

                with winreg.OpenKey(hkey, reg_path, 0, winreg.KEY_SET_VALUE) as key:
                    for name, data in values.items():
                        winreg.SetValueEx(key, name, 0, data["type"], data["value"])
            except Exception as e:
                print(f"Chyba při obnovování {path}: {e}")

    def detect_current_settings(self):
        """Detekuje současné nastavení PC a doporučí optimalizace"""
        self.diagnostic_text.delete(1.0, tk.END)
        self.diagnostic_text.insert(tk.END, "Detekuji současné nastavení...\n\n")

        threading.Thread(target=self._analyze_system, daemon=True).start()

    def _analyze_system(self):
        """Analyzuje systém a doporučí optimalizace"""
        try:
            # Detekce síťového adaptéru
            self.diagnostic_text.insert(tk.END, "=== SÍŤOVÝ ADAPTÉR ===\n")
            adapters = self._get_network_adapters()
            for adapter in adapters:
                self.diagnostic_text.insert(tk.END, f"Nalezen: {adapter}\n")
            self.root.update()

            # Detekce rychlosti připojení
            self.diagnostic_text.insert(tk.END, "\n=== RYCHLOST PŘIPOJENÍ ===\n")
            speed_info = self._detect_connection_speed()
            self.diagnostic_text.insert(tk.END, f"{speed_info}\n")
            self.root.update()

            # Kontrola DNS
            self.diagnostic_text.insert(tk.END, "\n=== DNS NASTAVENÍ ===\n")
            dns_info = self._check_dns_settings()
            self.diagnostic_text.insert(tk.END, f"{dns_info}\n")
            self.root.update()

            # Kontrola TCP nastavení
            self.diagnostic_text.insert(tk.END, "\n=== TCP NASTAVENÍ ===\n")
            tcp_info = self._check_tcp_settings()
            self.diagnostic_text.insert(tk.END, f"{tcp_info}\n")
            self.root.update()

            # Doporučení
            self.diagnostic_text.insert(tk.END, "\n=== DOPORUČENÍ ===\n")
            recommendations = self._generate_recommendations()
            for rec in recommendations:
                self.diagnostic_text.insert(tk.END, f"• {rec}\n")
            self.root.update()

        except Exception as e:
            self.diagnostic_text.insert(tk.END, f"\nChyba při analýze: {str(e)}\n")

    def _get_network_adapters(self):
        """Získá seznam síťových adaptérů"""
        adapters = []
        try:
            result = subprocess.run(['wmic', 'path', 'win32_networkadapter', 'get', 'name,netenabled'],
                                  capture_output=True, text=True)
            lines = result.stdout.split('\n')
            for line in lines[1:]:
                if 'TRUE' in line and line.strip():
                    adapter_name = line.replace('TRUE', '').strip()
                    if adapter_name and 'Name' not in adapter_name:
                        adapters.append(adapter_name)
        except:
            adapters.append("Nepodařilo se detekovat")
        return adapters

    def _detect_connection_speed(self):
        """Detekuje rychlost připojení"""
        try:
            result = subprocess.run(['netsh', 'wlan', 'show', 'interfaces'],
                                  capture_output=True, text=True)
            if 'Signal' in result.stdout:
                return "WiFi připojení detekováno"
            else:
                return "Kabelové připojení (pravděpodobně)"
        except:
            return "Nepodařilo se detekovat typ připojení"

    def _check_dns_settings(self):
        """Zkontroluje DNS nastavení"""
        try:
            result = subprocess.run(['nslookup', 'google.com'],
                                  capture_output=True, text=True)
            lines = result.stdout.split('\n')
            for line in lines:
                if 'Server:' in line:
                    dns_server = line.split(':')[1].strip()
                    if dns_server in ['*******', '*******', '*******', '*******']:
                        return f"Optimální DNS: {dns_server} ✓"
                    else:
                        return f"Současný DNS: {dns_server} (doporučujeme změnit)"
        except:
            pass
        return "Nepodařilo se zjistit DNS"

    def _check_tcp_settings(self):
        """Zkontroluje TCP nastavení"""
        try:
            result = subprocess.run(['netsh', 'int', 'tcp', 'show', 'global'],
                                  capture_output=True, text=True)

            settings = {}
            for line in result.stdout.split('\n'):
                if ':' in line:
                    key, value = line.split(':', 1)
                    settings[key.strip()] = value.strip()

            info = []
            if 'Receive Window Auto-Tuning Level' in settings:
                level = settings['Receive Window Auto-Tuning Level']
                if level == 'normal':
                    info.append("Auto-Tuning: Optimální ✓")
                else:
                    info.append(f"Auto-Tuning: {level} (doporučujeme 'normal')")

            return '\n'.join(info) if info else "TCP nastavení zkontrolováno"

        except:
            return "Nepodařilo se zkontrolovat TCP nastavení"

    def _generate_recommendations(self):
        """Generuje doporučení na základě analýzy"""
        recommendations = []

        # Vždy doporučí základní optimalizace
        recommendations.append("Vymazat DNS cache (ipconfig /flushdns)")
        recommendations.append("Optimalizovat TCP nastavení pro gaming")
        recommendations.append("Nastavit optimální DNS servery (*******, *******)")
        recommendations.append("Vypnout Nagle algoritmus pro nižší latenci")
        recommendations.append("Optimalizovat síťovou kartu")

        return recommendations

    def apply_smart_optimizations(self):
        """Aplikuje inteligentní optimalizace na základě detekce systému"""
        if not self.is_admin():
            messagebox.showerror("Chyba", "Pro optimalizace je potřeba spustit jako administrátor!")
            return

        # Vytvoří backup před optimalizacemi
        backup_file = self.create_backup()
        if not backup_file:
            return

        try:
            self.diagnostic_text.delete(1.0, tk.END)
            self.diagnostic_text.insert(tk.END, "Aplikuji inteligentní optimalizace...\n\n")

            optimizations = []

            # 1. Vymazání cache
            self.diagnostic_text.insert(tk.END, "Mazání cache...\n")
            self._clear_caches()
            optimizations.append("Cache vymazána")
            self.root.update()

            # 2. DNS optimalizace
            self.diagnostic_text.insert(tk.END, "Optimalizace DNS...\n")
            self._optimize_dns_smart()
            optimizations.append("DNS optimalizováno")
            self.root.update()

            # 3. TCP optimalizace
            self.diagnostic_text.insert(tk.END, "Optimalizace TCP...\n")
            self._optimize_tcp_smart()
            optimizations.append("TCP optimalizováno")
            self.root.update()

            # 4. Síťová karta
            self.diagnostic_text.insert(tk.END, "Optimalizace síťové karty...\n")
            self._optimize_network_smart()
            optimizations.append("Síťová karta optimalizována")
            self.root.update()

            # 5. Registry optimalizace
            self.diagnostic_text.insert(tk.END, "Registry optimalizace...\n")
            self._optimize_registry_smart()
            optimizations.append("Registry optimalizováno")
            self.root.update()

            self.diagnostic_text.insert(tk.END, f"\n✓ Dokončeno!\nBackup: {backup_file}\n")
            messagebox.showinfo("Úspěch",
                              f"Optimalizace dokončeny:\n" + "\n".join(optimizations) +
                              f"\n\nBackup: {backup_file}\nDoporučujeme restart počítače.")

        except Exception as e:
            messagebox.showerror("Chyba", f"Chyba při optimalizaci: {str(e)}")

    def _clear_caches(self):
        """Vymaže různé cache"""
        commands = [
            "ipconfig /flushdns",
            "netsh winsock reset",
            "netsh int ip reset"
        ]

        for cmd in commands:
            try:
                subprocess.run(cmd, shell=True, check=True)
            except:
                pass

    def _optimize_dns_smart(self):
        """Inteligentní DNS optimalizace"""
        try:
            # Detekuje aktivní síťové rozhraní a nastaví DNS
            result = subprocess.run(['netsh', 'interface', 'show', 'interface'],
                                  capture_output=True, text=True)

            interfaces = []
            for line in result.stdout.split('\n'):
                if 'Connected' in line and ('Ethernet' in line or 'Wi-Fi' in line):
                    parts = line.split()
                    interface_name = ' '.join(parts[3:])
                    interfaces.append(interface_name.strip())

            # Nastaví DNS pro všechna aktivní rozhraní
            for interface in interfaces:
                try:
                    subprocess.run(f'netsh interface ip set dns "{interface}" static *******',
                                 shell=True, check=True)
                    subprocess.run(f'netsh interface ip add dns "{interface}" ******* index=2',
                                 shell=True, check=True)
                except:
                    continue

        except Exception as e:
            print(f"Chyba při DNS optimalizaci: {e}")

    def _optimize_tcp_smart(self):
        """Inteligentní TCP optimalizace"""
        commands = [
            "netsh int tcp set global autotuninglevel=normal",
            "netsh int tcp set global chimney=enabled",
            "netsh int tcp set global rss=enabled",
            "netsh int tcp set global netdma=enabled"
        ]

        for cmd in commands:
            try:
                subprocess.run(cmd, shell=True, check=True)
            except:
                pass

    def _optimize_network_smart(self):
        """Inteligentní optimalizace síťové karty"""
        try:
            # Vypne power management pro síťové adaptéry
            subprocess.run(['powershell', '-Command',
                          'Get-NetAdapter | Where-Object {$_.Status -eq "Up"} | Get-NetAdapterPowerManagement | Set-NetAdapterPowerManagement -AllowComputerToTurnOffDevice Disabled'],
                          capture_output=True, text=True, check=True)
        except:
            pass

    def _optimize_registry_smart(self):
        """Inteligentní registry optimalizace"""
        try:
            # Vypne Nagle algoritmus pro všechna síťová rozhraní
            interfaces_key = r"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters\Interfaces"
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, interfaces_key) as key:
                for i in range(winreg.QueryInfoKey(key)[0]):
                    try:
                        interface_name = winreg.EnumKey(key, i)
                        interface_path = f"{interfaces_key}\\{interface_name}"
                        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, interface_path, 0,
                                          winreg.KEY_SET_VALUE) as interface_key:
                            winreg.SetValueEx(interface_key, "TcpAckFrequency", 0,
                                            winreg.REG_DWORD, 1)
                            winreg.SetValueEx(interface_key, "TCPNoDelay", 0,
                                            winreg.REG_DWORD, 1)
                    except:
                        continue
        except Exception as e:
            print(f"Chyba při registry optimalizaci: {e}")

    def is_admin(self):
        """Zkontroluje, zda je aplikace spuštěna jako administrátor"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False

    def _run_cmd(self, command):
        """Spustí příkaz v cmd"""
        subprocess.run(command, shell=True, check=True)

    def speed_test(self):
        """Spustí speed test"""
        self.diagnostic_text.delete(1.0, tk.END)
        self.diagnostic_text.insert(tk.END, "Spouštím speed test...\n")
        self.diagnostic_text.insert(tk.END, "Otevírám fast.com v prohlížeči...\n")

        try:
            import webbrowser
            webbrowser.open("https://fast.com")
            self.diagnostic_text.insert(tk.END, "Speed test spuštěn v prohlížeči.\n")
        except Exception as e:
            self.diagnostic_text.insert(tk.END, f"Chyba: {str(e)}\n")

    def ping_test(self):
        """Spustí ping test"""
        self.diagnostic_text.delete(1.0, tk.END)
        self.diagnostic_text.insert(tk.END, "Spouštím ping test...\n")
        threading.Thread(target=self._run_ping_test, daemon=True).start()

    def _run_ping_test(self):
        """Spustí ping test v separátním vlákně"""
        hosts = {
            "Google DNS": "*******",
            "Cloudflare DNS": "*******",
            "CS2 Server EU": "************",
            "Steam": "steamcommunity.com"
        }

        for name, host in hosts.items():
            self.diagnostic_text.insert(tk.END, f"\nTestuji {name} ({host})...\n")
            self.root.update()

            try:
                result = subprocess.run(['ping', '-n', '10', host],
                                      capture_output=True, text=True, timeout=30)
                if result.returncode == 0:
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if 'Minimum' in line or 'Maximum' in line or 'Average' in line:
                            self.diagnostic_text.insert(tk.END, f"  {line.strip()}\n")
                        elif 'Průměr' in line or 'Minimum' in line or 'Maximum' in line:
                            self.diagnostic_text.insert(tk.END, f"  {line.strip()}\n")
                else:
                    self.diagnostic_text.insert(tk.END, f"  FAILED\n")
            except Exception as e:
                self.diagnostic_text.insert(tk.END, f"  ERROR: {str(e)}\n")

            self.root.update()

    def load_settings(self):
        """Načte uložená nastavení"""
        try:
            settings_file = Path("cs2_optimizer_settings.json")
            if settings_file.exists():
                with open(settings_file, 'r') as f:
                    settings = json.load(f)

                # Načte profil
                if "selected_profile" in settings:
                    self.selected_profile.set(settings["selected_profile"])

                # Načte CS2 příkazy
                if "cs2_commands" in settings:
                    for cmd, value in settings["cs2_commands"].items():
                        if cmd in self.cs2_commands:
                            self.cs2_commands[cmd].set(value)
        except Exception as e:
            print(f"Chyba při načítání nastavení: {e}")

    def save_settings(self):
        """Uloží nastavení"""
        try:
            settings = {
                "selected_profile": self.selected_profile.get(),
                "cs2_commands": {cmd: var.get() for cmd, var in self.cs2_commands.items()}
            }

            with open("cs2_optimizer_settings.json", 'w') as f:
                json.dump(settings, f, indent=2)
        except Exception as e:
            print(f"Chyba při ukládání nastavení: {e}")

    def run(self):
        """Spustí aplikaci"""
        # Uloží nastavení při zavření
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
        self.root.mainloop()

    def _on_closing(self):
        """Volá se při zavírání aplikace"""
        self.save_settings()
        self.root.destroy()

    def apply_windows_optimizations(self):
        """Aplikuje Windows síťové optimalizace"""
        if not self.is_admin():
            messagebox.showerror("Chyba", "Pro Windows optimalizace je potřeba spustit aplikaci jako administrátor!")
            return

        try:
            applied = []

            if self.windows_optimizations["tcp_window_scaling"].get():
                self._run_cmd("netsh int tcp set global autotuninglevel=normal")
                applied.append("TCP Window Scaling")

            if self.windows_optimizations["tcp_chimney"].get():
                self._run_cmd("netsh int tcp set global chimney=enabled")
                self._run_cmd("netsh int tcp set global rss=enabled")
                applied.append("TCP Chimney Offload")

            if self.windows_optimizations["nagle_disable"].get():
                self._disable_nagle_algorithm()
                applied.append("Nagle algoritmus vypnut")

            if self.windows_optimizations["interrupt_moderation"].get():
                self._optimize_network_adapter()
                applied.append("Síťová karta optimalizována")

            if self.windows_optimizations["power_management"].get():
                self._disable_power_management()
                applied.append("Úspora energie vypnuta")

            if self.windows_optimizations["game_mode"].get():
                self._enable_game_mode()
                applied.append("Game Mode zapnut")

            if self.windows_optimizations["dns_optimization"].get():
                self._optimize_dns()
                applied.append("DNS optimalizováno")

            if applied:
                messagebox.showinfo("Úspěch", f"Aplikováno:\n" + "\n".join(applied) +
                                  "\n\nDoporučujeme restart počítače.")
            else:
                messagebox.showinfo("Info", "Žádné optimalizace nebyly vybrány.")

        except Exception as e:
            messagebox.showerror("Chyba", f"Chyba při aplikování optimalizací: {str(e)}")

    def is_admin(self):
        """Zkontroluje, zda je aplikace spuštěna jako administrátor"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False

    def _run_cmd(self, command):
        """Spustí příkaz v cmd"""
        subprocess.run(command, shell=True, check=True)

    def _disable_nagle_algorithm(self):
        """Vypne Nagle algoritmus"""
        try:
            # Najde síťové adaptéry a vypne Nagle
            interfaces_key = r"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters\Interfaces"
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, interfaces_key) as key:
                for i in range(winreg.QueryInfoKey(key)[0]):
                    interface_name = winreg.EnumKey(key, i)
                    try:
                        interface_path = f"{interfaces_key}\\{interface_name}"
                        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, interface_path, 0,
                                          winreg.KEY_SET_VALUE) as interface_key:
                            winreg.SetValueEx(interface_key, "TcpAckFrequency", 0,
                                            winreg.REG_DWORD, 1)
                            winreg.SetValueEx(interface_key, "TCPNoDelay", 0,
                                            winreg.REG_DWORD, 1)
                    except:
                        continue
        except Exception as e:
            print(f"Chyba při vypínání Nagle: {e}")

    def _optimize_network_adapter(self):
        """Optimalizuje nastavení síťové karty"""
        try:
            # Optimalizuje síťové adaptéry pomocí PowerShell
            subprocess.run(['powershell', '-Command',
                          'Get-NetAdapter | Where-Object {$_.Status -eq "Up"} | Set-NetAdapterAdvancedProperty -DisplayName "Interrupt Moderation" -DisplayValue "Disabled"'],
                          capture_output=True, text=True)
            print("Síťové adaptéry optimalizovány")
        except Exception as e:
            print(f"Chyba při optimalizaci adaptéru: {e}")

    def _disable_power_management(self):
        """Vypne úsporu energie pro USB a síť"""
        try:
            # Vypne USB selective suspend
            power_key = r"SYSTEM\CurrentControlSet\Services\USB"
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, power_key, 0,
                              winreg.KEY_SET_VALUE) as key:
                winreg.SetValueEx(key, "DisableSelectiveSuspend", 0, winreg.REG_DWORD, 1)
        except Exception as e:
            print(f"Chyba při vypínání power management: {e}")

    def _enable_game_mode(self):
        """Zapne Windows Game Mode"""
        try:
            game_mode_key = r"SOFTWARE\Microsoft\GameBar"
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, game_mode_key, 0,
                              winreg.KEY_SET_VALUE) as key:
                winreg.SetValueEx(key, "AutoGameModeEnabled", 0, winreg.REG_DWORD, 1)
        except Exception as e:
            print(f"Chyba při zapínání Game Mode: {e}")

    def _optimize_dns(self):
        """Nastaví optimální DNS servery"""
        try:
            # Nastaví DNS přes netsh
            self._run_cmd('netsh interface ip set dns "Ethernet" static *******')
            self._run_cmd('netsh interface ip add dns "Ethernet" ******* index=2')
            self._run_cmd('netsh interface ip set dns "Wi-Fi" static *******')
            self._run_cmd('netsh interface ip add dns "Wi-Fi" ******* index=2')
            self._run_cmd('ipconfig /flushdns')
        except Exception as e:
            print(f"Chyba při nastavování DNS: {e}")

    def speed_test(self):
        """Spustí speed test"""
        self.diagnostic_text.delete(1.0, tk.END)
        self.diagnostic_text.insert(tk.END, "Spouštím speed test...\n")
        self.diagnostic_text.insert(tk.END, "Otevírám fast.com v prohlížeči...\n")

        try:
            import webbrowser
            webbrowser.open("https://fast.com")
            self.diagnostic_text.insert(tk.END, "Speed test spuštěn v prohlížeči.\n")
        except Exception as e:
            self.diagnostic_text.insert(tk.END, f"Chyba: {str(e)}\n")

if __name__ == "__main__":
    app = CS2NetworkOptimizer()
    app.run()
