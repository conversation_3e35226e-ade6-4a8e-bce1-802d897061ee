#!/usr/bin/env python3
"""
CS2 Network Optimizer
Optimalizuje Counter-Strike 2 pro různé typy připojení včetně satelitního internetu
Autor: Augment Agent
"""

import os
import sys
import json
import subprocess
import winreg
import ctypes
import time
import threading
from pathlib import Path
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import requests
import psutil

class CS2NetworkOptimizer:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("CS2 Network Optimizer v1.0")
        self.root.geometry("800x600")
        self.root.resizable(True, True)

        # Konfigurace pro různé typy připojení
        self.connection_profiles = {
            "satellite": {
                "name": "Satelitní (Starnet, Starlink)",
                "rate": "160000",
                "cl_updaterate": "64",
                "cl_cmdrate": "64",
                "cl_interp": "0.015625",
                "cl_interp_ratio": "2",
                "net_maxroutable": "1200",
                "fps_max": "165",
                "description": "Optimalizováno pro satelitní připojení s vyšší latencí"
            },
            "fiber": {
                "name": "Optika/Kabel",
                "rate": "786432",
                "cl_updaterate": "128",
                "cl_cmdrate": "128",
                "cl_interp": "0",
                "cl_interp_ratio": "1",
                "net_maxroutable": "1460",
                "fps_max": "240",
                "description": "Optimalizováno pro rychlé stabilní připojení"
            },
            "wifi": {
                "name": "WiFi",
                "rate": "196608",
                "cl_updaterate": "64",
                "cl_cmdrate": "64",
                "cl_interp": "0.007813",
                "cl_interp_ratio": "1",
                "net_maxroutable": "1200",
                "fps_max": "144",
                "description": "Optimalizováno pro WiFi s možným jitterem"
            },
            "mobile": {
                "name": "Mobilní data",
                "rate": "128000",
                "cl_updaterate": "64",
                "cl_cmdrate": "64",
                "cl_interp": "0.03125",
                "cl_interp_ratio": "2",
                "net_maxroutable": "1200",
                "fps_max": "120",
                "description": "Optimalizováno pro mobilní připojení"
            }
        }

        self.setup_ui()
        self.load_settings()

    def setup_ui(self):
        # Hlavní notebook
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill="both", expand=True, padx=10, pady=10)

        # Tab 1: Profily připojení
        self.profile_frame = ttk.Frame(notebook)
        notebook.add(self.profile_frame, text="Profily připojení")
        self.setup_profile_tab()

        # Tab 2: Pokročilé nastavení
        self.advanced_frame = ttk.Frame(notebook)
        notebook.add(self.advanced_frame, text="Pokročilé nastavení")
        self.setup_advanced_tab()

        # Tab 3: Diagnostika
        self.diagnostic_frame = ttk.Frame(notebook)
        notebook.add(self.diagnostic_frame, text="Diagnostika")
        self.setup_diagnostic_tab()

        # Tab 4: Windows optimalizace
        self.windows_frame = ttk.Frame(notebook)
        notebook.add(self.windows_frame, text="Windows optimalizace")
        self.setup_windows_tab()

    def setup_profile_tab(self):
        # Výběr profilu
        ttk.Label(self.profile_frame, text="Vyberte typ vašeho připojení:", font=("Arial", 12, "bold")).pack(pady=10)

        self.selected_profile = tk.StringVar(value="satellite")

        for key, profile in self.connection_profiles.items():
            frame = ttk.LabelFrame(self.profile_frame, text=profile["name"], padding=10)
            frame.pack(fill="x", padx=20, pady=5)

            ttk.Radiobutton(frame, text=profile["description"],
                          variable=self.selected_profile, value=key).pack(anchor="w")

            # Zobrazení nastavení
            settings_text = f"Rate: {profile['rate']}, Update/Cmd: {profile['cl_updaterate']}/{profile['cl_cmdrate']}, FPS: {profile['fps_max']}"
            ttk.Label(frame, text=settings_text, font=("Arial", 8)).pack(anchor="w")

        # Tlačítka
        button_frame = ttk.Frame(self.profile_frame)
        button_frame.pack(pady=20)

        ttk.Button(button_frame, text="Aplikovat profil",
                  command=self.apply_profile, style="Accent.TButton").pack(side="left", padx=5)
        ttk.Button(button_frame, text="Test připojení",
                  command=self.test_connection).pack(side="left", padx=5)

    def setup_advanced_tab(self):
        # Pokročilé CS2 nastavení
        ttk.Label(self.advanced_frame, text="Pokročilé CS2 nastavení", font=("Arial", 12, "bold")).pack(pady=10)

        # Scrollable frame pro nastavení
        canvas = tk.Canvas(self.advanced_frame)
        scrollbar = ttk.Scrollbar(self.advanced_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # CS2 příkazy
        self.cs2_commands = {
            "rate": tk.StringVar(value="160000"),
            "cl_updaterate": tk.StringVar(value="64"),
            "cl_cmdrate": tk.StringVar(value="64"),
            "cl_interp": tk.StringVar(value="0"),
            "cl_interp_ratio": tk.StringVar(value="1"),
            "fps_max": tk.StringVar(value="165"),
            "net_maxroutable": tk.StringVar(value="1200"),
            "cl_predict": tk.StringVar(value="1"),
            "cl_lagcompensation": tk.StringVar(value="1"),
            "net_splitpacket_maxrate": tk.StringVar(value="50000")
        }

        for cmd, var in self.cs2_commands.items():
            frame = ttk.Frame(scrollable_frame)
            frame.pack(fill="x", padx=10, pady=2)
            ttk.Label(frame, text=f"{cmd}:", width=20).pack(side="left")
            ttk.Entry(frame, textvariable=var, width=15).pack(side="left", padx=5)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Tlačítka
        ttk.Button(self.advanced_frame, text="Generovat autoexec.cfg",
                  command=self.generate_autoexec).pack(pady=10)

    def setup_diagnostic_tab(self):
        ttk.Label(self.diagnostic_frame, text="Diagnostika připojení", font=("Arial", 12, "bold")).pack(pady=10)

        # Výsledky testů
        self.diagnostic_text = scrolledtext.ScrolledText(self.diagnostic_frame, height=20, width=80)
        self.diagnostic_text.pack(fill="both", expand=True, padx=10, pady=10)

        # Tlačítka pro testy
        test_frame = ttk.Frame(self.diagnostic_frame)
        test_frame.pack(pady=10)

        ttk.Button(test_frame, text="Test rychlosti", command=self.speed_test).pack(side="left", padx=5)
        ttk.Button(test_frame, text="Ping test", command=self.ping_test).pack(side="left", padx=5)
        ttk.Button(test_frame, text="Buffer bloat test", command=self.buffer_bloat_test).pack(side="left", padx=5)
        ttk.Button(test_frame, text="CS2 servery test", command=self.cs2_servers_test).pack(side="left", padx=5)

    def setup_windows_tab(self):
        ttk.Label(self.windows_frame, text="Windows síťové optimalizace", font=("Arial", 12, "bold")).pack(pady=10)

        # Checkboxy pro optimalizace
        self.windows_optimizations = {
            "tcp_window_scaling": tk.BooleanVar(value=True),
            "tcp_chimney": tk.BooleanVar(value=True),
            "nagle_disable": tk.BooleanVar(value=True),
            "interrupt_moderation": tk.BooleanVar(value=True),
            "power_management": tk.BooleanVar(value=True),
            "game_mode": tk.BooleanVar(value=True),
            "dns_optimization": tk.BooleanVar(value=True)
        }

        optimizations_desc = {
            "tcp_window_scaling": "TCP Window Scaling (pro satelitní připojení)",
            "tcp_chimney": "TCP Chimney Offload",
            "nagle_disable": "Vypnout Nagle algoritmus",
            "interrupt_moderation": "Optimalizovat síťovou kartu",
            "power_management": "Vypnout úsporu energie USB/síť",
            "game_mode": "Zapnout Windows Game Mode",
            "dns_optimization": "Optimalizovat DNS (*******, *******)"
        }

        for key, var in self.windows_optimizations.items():
            ttk.Checkbutton(self.windows_frame, text=optimizations_desc[key],
                          variable=var).pack(anchor="w", padx=20, pady=2)

        ttk.Button(self.windows_frame, text="Aplikovat Windows optimalizace",
                  command=self.apply_windows_optimizations).pack(pady=20)

        # Varování
        warning_text = "⚠️ Některé optimalizace vyžadují restart počítače\n⚠️ Spusťte aplikaci jako administrátor pro plnou funkcionalitu"
        ttk.Label(self.windows_frame, text=warning_text, foreground="red").pack(pady=10)

    def apply_profile(self):
        """Aplikuje vybraný profil připojení"""
        profile = self.connection_profiles[self.selected_profile.get()]

        # Aktualizuje pokročilé nastavení podle profilu
        for cmd, value in profile.items():
            if cmd in self.cs2_commands:
                self.cs2_commands[cmd].set(value)

        # Generuje autoexec.cfg
        self.generate_autoexec()
        messagebox.showinfo("Úspěch", f"Profil '{profile['name']}' byl aplikován!")

    def generate_autoexec(self):
        """Generuje autoexec.cfg soubor pro CS2"""
        try:
            # Najde CS2 složku
            cs2_path = self.find_cs2_path()
            if not cs2_path:
                messagebox.showerror("Chyba", "CS2 instalace nebyla nalezena!")
                return

            # Vytvoří autoexec.cfg obsah
            config_content = self.create_autoexec_content()

            # Uloží soubor
            autoexec_path = cs2_path / "game" / "csgo" / "cfg" / "autoexec.cfg"
            autoexec_path.parent.mkdir(parents=True, exist_ok=True)

            with open(autoexec_path, 'w', encoding='utf-8') as f:
                f.write(config_content)

            messagebox.showinfo("Úspěch", f"autoexec.cfg byl vytvořen:\n{autoexec_path}")

        except Exception as e:
            messagebox.showerror("Chyba", f"Nepodařilo se vytvořit autoexec.cfg: {str(e)}")

    def create_autoexec_content(self):
        """Vytvoří obsah autoexec.cfg"""
        content = [
            "// CS2 Network Optimizer - Automaticky generovaný config",
            "// Profil: " + self.connection_profiles[self.selected_profile.get()]["name"],
            "// Generováno: " + time.strftime("%Y-%m-%d %H:%M:%S"),
            "",
            "// Síťové nastavení",
        ]

        for cmd, var in self.cs2_commands.items():
            content.append(f"{cmd} {var.get()}")

        content.extend([
            "",
            "// Další optimalizace",
            "cl_predict 1",
            "cl_lagcompensation 1",
            "net_graph 1",
            "cl_showfps 1",
            "",
            "// Satelitní specifické optimalizace",
            "net_maxcleartime 0.001",
            "cl_timeout 30",
            "",
            "echo 'CS2 Network Optimizer config loaded!'"
        ])

        return "\n".join(content)

    def find_cs2_path(self):
        """Najde cestu k CS2 instalaci"""
        possible_paths = [
            Path("C:/Program Files (x86)/Steam/steamapps/common/Counter-Strike Global Offensive"),
            Path("C:/Program Files/Steam/steamapps/common/Counter-Strike Global Offensive"),
            Path("D:/Steam/steamapps/common/Counter-Strike Global Offensive"),
            Path("E:/Steam/steamapps/common/Counter-Strike Global Offensive")
        ]

        for path in possible_paths:
            if path.exists():
                return path

        # Pokusí se najít přes registry
        try:
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                              r"SOFTWARE\WOW6432Node\Valve\Steam") as key:
                steam_path = winreg.QueryValueEx(key, "InstallPath")[0]
                cs2_path = Path(steam_path) / "steamapps" / "common" / "Counter-Strike Global Offensive"
                if cs2_path.exists():
                    return cs2_path
        except:
            pass

        return None

    def test_connection(self):
        """Spustí test připojení"""
        self.diagnostic_text.delete(1.0, tk.END)
        self.diagnostic_text.insert(tk.END, "Spouštím test připojení...\n")
        self.root.update()

        # Spustí testy v separátním vlákně
        threading.Thread(target=self._run_connection_tests, daemon=True).start()

    def _run_connection_tests(self):
        """Spustí testy připojení v separátním vlákně"""
        try:
            # Ping test
            self.diagnostic_text.insert(tk.END, "\n=== PING TEST ===\n")
            self.root.update()

            ping_results = self._ping_test_internal(["*******", "*******", "************"])
            for host, result in ping_results.items():
                self.diagnostic_text.insert(tk.END, f"{host}: {result}\n")
                self.root.update()

            # Speed test (zjednodušený)
            self.diagnostic_text.insert(tk.END, "\n=== RYCHLOST ===\n")
            self.diagnostic_text.insert(tk.END, "Pro přesný speed test použijte fast.com nebo speedtest.net\n")
            self.root.update()

            # Doporučení na základě profilu
            profile = self.connection_profiles[self.selected_profile.get()]
            self.diagnostic_text.insert(tk.END, f"\n=== DOPORUČENÍ PRO {profile['name'].upper()} ===\n")
            self.diagnostic_text.insert(tk.END, f"Rate: {profile['rate']}\n")
            self.diagnostic_text.insert(tk.END, f"Update/Cmd rate: {profile['cl_updaterate']}/{profile['cl_cmdrate']}\n")
            self.diagnostic_text.insert(tk.END, f"Max FPS: {profile['fps_max']}\n")
            self.root.update()

        except Exception as e:
            self.diagnostic_text.insert(tk.END, f"\nChyba při testování: {str(e)}\n")
            self.root.update()

    def _ping_test_internal(self, hosts):
        """Interní ping test"""
        results = {}
        for host in hosts:
            try:
                result = subprocess.run(['ping', '-n', '4', host],
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    # Parsuje výsledek pingu
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if 'Average' in line or 'Průměr' in line:
                            results[host] = line.strip()
                            break
                    else:
                        results[host] = "OK"
                else:
                    results[host] = "FAILED"
            except:
                results[host] = "TIMEOUT"
        return results

    def apply_windows_optimizations(self):
        """Aplikuje Windows síťové optimalizace"""
        if not self.is_admin():
            messagebox.showerror("Chyba", "Pro Windows optimalizace je potřeba spustit aplikaci jako administrátor!")
            return

        try:
            applied = []

            if self.windows_optimizations["tcp_window_scaling"].get():
                self._run_cmd("netsh int tcp set global autotuninglevel=normal")
                applied.append("TCP Window Scaling")

            if self.windows_optimizations["tcp_chimney"].get():
                self._run_cmd("netsh int tcp set global chimney=enabled")
                self._run_cmd("netsh int tcp set global rss=enabled")
                applied.append("TCP Chimney Offload")

            if self.windows_optimizations["nagle_disable"].get():
                self._disable_nagle_algorithm()
                applied.append("Nagle algoritmus vypnut")

            if self.windows_optimizations["interrupt_moderation"].get():
                self._optimize_network_adapter()
                applied.append("Síťová karta optimalizována")

            if self.windows_optimizations["power_management"].get():
                self._disable_power_management()
                applied.append("Úspora energie vypnuta")

            if self.windows_optimizations["game_mode"].get():
                self._enable_game_mode()
                applied.append("Game Mode zapnut")

            if self.windows_optimizations["dns_optimization"].get():
                self._optimize_dns()
                applied.append("DNS optimalizováno")

            if applied:
                messagebox.showinfo("Úspěch", f"Aplikováno:\n" + "\n".join(applied) +
                                  "\n\nDoporučujeme restart počítače.")
            else:
                messagebox.showinfo("Info", "Žádné optimalizace nebyly vybrány.")

        except Exception as e:
            messagebox.showerror("Chyba", f"Chyba při aplikování optimalizací: {str(e)}")

    def is_admin(self):
        """Zkontroluje, zda je aplikace spuštěna jako administrátor"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False

    def _run_cmd(self, command):
        """Spustí příkaz v cmd"""
        subprocess.run(command, shell=True, check=True)

    def _disable_nagle_algorithm(self):
        """Vypne Nagle algoritmus"""
        try:
            # Najde síťové adaptéry a vypne Nagle
            interfaces_key = r"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters\Interfaces"
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, interfaces_key) as key:
                for i in range(winreg.QueryInfoKey(key)[0]):
                    interface_name = winreg.EnumKey(key, i)
                    try:
                        interface_path = f"{interfaces_key}\\{interface_name}"
                        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, interface_path, 0,
                                          winreg.KEY_SET_VALUE) as interface_key:
                            winreg.SetValueEx(interface_key, "TcpAckFrequency", 0,
                                            winreg.REG_DWORD, 1)
                            winreg.SetValueEx(interface_key, "TCPNoDelay", 0,
                                            winreg.REG_DWORD, 1)
                    except:
                        continue
        except Exception as e:
            print(f"Chyba při vypínání Nagle: {e}")

    def _optimize_network_adapter(self):
        """Optimalizuje nastavení síťové karty"""
        try:
            # Najde síťové adaptéry pomocí WMI
            import wmi
            c = wmi.WMI()
            for adapter in c.Win32_NetworkAdapter():
                if adapter.NetEnabled and adapter.PhysicalAdapter:
                    # Zde by bylo potřeba upravit registry pro konkrétní adaptér
                    # Pro zjednodušení pouze logujeme
                    print(f"Optimalizuji adaptér: {adapter.Name}")
        except ImportError:
            # Fallback bez WMI
            print("WMI není dostupné, přeskakuji optimalizaci adaptéru")
        except Exception as e:
            print(f"Chyba při optimalizaci adaptéru: {e}")

    def _disable_power_management(self):
        """Vypne úsporu energie pro USB a síť"""
        try:
            # Vypne USB selective suspend
            power_key = r"SYSTEM\CurrentControlSet\Services\USB"
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, power_key, 0,
                              winreg.KEY_SET_VALUE) as key:
                winreg.SetValueEx(key, "DisableSelectiveSuspend", 0, winreg.REG_DWORD, 1)
        except Exception as e:
            print(f"Chyba při vypínání power management: {e}")

    def _enable_game_mode(self):
        """Zapne Windows Game Mode"""
        try:
            game_mode_key = r"SOFTWARE\Microsoft\GameBar"
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, game_mode_key, 0,
                              winreg.KEY_SET_VALUE) as key:
                winreg.SetValueEx(key, "AutoGameModeEnabled", 0, winreg.REG_DWORD, 1)
        except Exception as e:
            print(f"Chyba při zapínání Game Mode: {e}")

    def _optimize_dns(self):
        """Nastaví optimální DNS servery"""
        try:
            # Nastaví DNS přes netsh
            self._run_cmd('netsh interface ip set dns "Ethernet" static *******')
            self._run_cmd('netsh interface ip add dns "Ethernet" ******* index=2')
            self._run_cmd('netsh interface ip set dns "Wi-Fi" static *******')
            self._run_cmd('netsh interface ip add dns "Wi-Fi" ******* index=2')
            self._run_cmd('ipconfig /flushdns')
        except Exception as e:
            print(f"Chyba při nastavování DNS: {e}")

    def speed_test(self):
        """Spustí speed test"""
        self.diagnostic_text.delete(1.0, tk.END)
        self.diagnostic_text.insert(tk.END, "Spouštím speed test...\n")
        self.diagnostic_text.insert(tk.END, "Otevírám fast.com v prohlížeči...\n")

        try:
            import webbrowser
            webbrowser.open("https://fast.com")
            self.diagnostic_text.insert(tk.END, "Speed test spuštěn v prohlížeči.\n")
        except Exception as e:
            self.diagnostic_text.insert(tk.END, f"Chyba: {str(e)}\n")

    def ping_test(self):
        """Spustí ping test"""
        self.diagnostic_text.delete(1.0, tk.END)
        self.diagnostic_text.insert(tk.END, "Spouštím ping test...\n")
        threading.Thread(target=self._run_ping_test, daemon=True).start()

    def _run_ping_test(self):
        """Spustí ping test v separátním vlákně"""
        hosts = {
            "Google DNS": "*******",
            "Cloudflare DNS": "*******",
            "CS2 Server EU": "************",
            "Steam": "steamcommunity.com"
        }

        for name, host in hosts.items():
            self.diagnostic_text.insert(tk.END, f"\nTestuji {name} ({host})...\n")
            self.root.update()

            try:
                result = subprocess.run(['ping', '-n', '10', host],
                                      capture_output=True, text=True, timeout=30)
                if result.returncode == 0:
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if 'Minimum' in line or 'Maximum' in line or 'Average' in line:
                            self.diagnostic_text.insert(tk.END, f"  {line.strip()}\n")
                        elif 'Průměr' in line or 'Minimum' in line or 'Maximum' in line:
                            self.diagnostic_text.insert(tk.END, f"  {line.strip()}\n")
                else:
                    self.diagnostic_text.insert(tk.END, f"  FAILED\n")
            except Exception as e:
                self.diagnostic_text.insert(tk.END, f"  ERROR: {str(e)}\n")

            self.root.update()

    def buffer_bloat_test(self):
        """Spustí buffer bloat test"""
        self.diagnostic_text.delete(1.0, tk.END)
        self.diagnostic_text.insert(tk.END, "Spouštím buffer bloat test...\n")
        self.diagnostic_text.insert(tk.END, "Otevírám dslreports.com/speedtest v prohlížeči...\n")

        try:
            import webbrowser
            webbrowser.open("http://www.dslreports.com/speedtest")
            self.diagnostic_text.insert(tk.END, "Buffer bloat test spuštěn v prohlížeči.\n")
            self.diagnostic_text.insert(tk.END, "Sledujte 'Buffer Bloat' hodnocení (A-F).\n")
        except Exception as e:
            self.diagnostic_text.insert(tk.END, f"Chyba: {str(e)}\n")

    def cs2_servers_test(self):
        """Testuje CS2 servery"""
        self.diagnostic_text.delete(1.0, tk.END)
        self.diagnostic_text.insert(tk.END, "Testuji CS2 servery...\n")
        threading.Thread(target=self._run_cs2_servers_test, daemon=True).start()

    def _run_cs2_servers_test(self):
        """Testuje CS2 servery v separátním vlákně"""
        cs2_servers = {
            "EU West": "************",
            "EU East": "************",
            "EU North": "************"
        }

        for name, ip in cs2_servers.items():
            self.diagnostic_text.insert(tk.END, f"\nTestuji {name} ({ip})...\n")
            self.root.update()

            try:
                result = subprocess.run(['ping', '-n', '5', ip],
                                      capture_output=True, text=True, timeout=15)
                if result.returncode == 0:
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if 'Average' in line or 'Průměr' in line:
                            self.diagnostic_text.insert(tk.END, f"  {line.strip()}\n")
                            break
                else:
                    self.diagnostic_text.insert(tk.END, f"  NEDOSTUPNÝ\n")
            except:
                self.diagnostic_text.insert(tk.END, f"  TIMEOUT\n")

            self.root.update()

    def load_settings(self):
        """Načte uložená nastavení"""
        try:
            settings_file = Path("cs2_optimizer_settings.json")
            if settings_file.exists():
                with open(settings_file, 'r') as f:
                    settings = json.load(f)

                # Načte profil
                if "selected_profile" in settings:
                    self.selected_profile.set(settings["selected_profile"])

                # Načte CS2 příkazy
                if "cs2_commands" in settings:
                    for cmd, value in settings["cs2_commands"].items():
                        if cmd in self.cs2_commands:
                            self.cs2_commands[cmd].set(value)
        except Exception as e:
            print(f"Chyba při načítání nastavení: {e}")

    def save_settings(self):
        """Uloží nastavení"""
        try:
            settings = {
                "selected_profile": self.selected_profile.get(),
                "cs2_commands": {cmd: var.get() for cmd, var in self.cs2_commands.items()}
            }

            with open("cs2_optimizer_settings.json", 'w') as f:
                json.dump(settings, f, indent=2)
        except Exception as e:
            print(f"Chyba při ukládání nastavení: {e}")

    def run(self):
        """Spustí aplikaci"""
        # Uloží nastavení při zavření
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
        self.root.mainloop()

    def _on_closing(self):
        """Volá se při zavírání aplikace"""
        self.save_settings()
        self.root.destroy()

if __name__ == "__main__":
    app = CS2NetworkOptimizer()
    app.run()
