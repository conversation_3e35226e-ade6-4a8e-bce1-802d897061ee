#!/bin/bash

# MindFlow - Quick Test Setup Script
# Rychlé nastavení pro testování aplikace

echo "🧠 MindFlow - Quick Test Setup"
echo "=============================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    log_error "package.json not found. Please run this script from the project root."
    exit 1
fi

log_info "Setting up MindFlow for testing..."

# 1. Install dependencies
log_info "Installing dependencies..."
if command -v yarn &> /dev/null; then
    yarn install
else
    npm install
fi

# 2. Create test data directory
mkdir -p test-data

# 3. Create mock data for testing
log_info "Creating test data..."

cat > test-data/mock-users.json << 'EOF'
{
  "testUsers": [
    {
      "id": "test_user_1",
      "name": "Anna Testová",
      "email": "<EMAIL>",
      "joinDate": "2024-01-01",
      "streakDays": 15,
      "totalMeditations": 45,
      "averageMood": 7.2,
      "isPremium": false
    },
    {
      "id": "test_user_2", 
      "name": "Petr Zkušební",
      "email": "<EMAIL>",
      "joinDate": "2024-01-15",
      "streakDays": 7,
      "totalMeditations": 12,
      "averageMood": 6.8,
      "isPremium": true
    }
  ],
  "moodHistory": [
    {"date": "2024-01-20", "mood": "good", "timestamp": 1705747200000},
    {"date": "2024-01-21", "mood": "amazing", "timestamp": 1705833600000},
    {"date": "2024-01-22", "mood": "okay", "timestamp": 1705920000000},
    {"date": "2024-01-23", "mood": "good", "timestamp": 1706006400000},
    {"date": "2024-01-24", "mood": "bad", "timestamp": 1706092800000}
  ]
}
EOF

# 4. Create test configuration
cat > test-config.js << 'EOF'
// MindFlow Test Configuration
export const TEST_CONFIG = {
  // Enable test mode
  TEST_MODE: true,
  
  // Mock data
  USE_MOCK_DATA: true,
  
  // Skip real API calls
  SKIP_ANALYTICS: true,
  SKIP_ADS: false, // Keep ads for testing monetization
  
  // Test user
  TEST_USER: {
    id: 'test_user_demo',
    name: 'Demo Uživatel',
    email: '<EMAIL>',
    isPremium: false,
    streakDays: 3,
    totalMeditations: 8
  },
  
  // Test ads (using AdMob test IDs)
  TEST_AD_UNITS: {
    android: {
      banner: 'ca-app-pub-3940256099942544/6300978111',
      interstitial: 'ca-app-pub-3940256099942544/1033173712',
      rewarded: 'ca-app-pub-3940256099942544/5224354917'
    },
    ios: {
      banner: 'ca-app-pub-3940256099942544/2934735716',
      interstitial: 'ca-app-pub-3940256099942544/4411468910', 
      rewarded: 'ca-app-pub-3940256099942544/1712485313'
    }
  },
  
  // Accelerated timers for testing
  FAST_MODE: {
    notificationInterval: 10000, // 10 seconds instead of hours
    adRotationInterval: 5000,    // 5 seconds instead of minutes
    analyticsSync: 30000         // 30 seconds instead of 5 minutes
  }
};
EOF

# 5. Create test runner script
cat > run-tests.js << 'EOF'
const { execSync } = require('child_process');
const fs = require('fs');

console.log('🧠 MindFlow Test Runner');
console.log('======================');

// Test scenarios
const testScenarios = [
  {
    name: 'Mood Tracking Test',
    description: 'Test mood selection and analytics',
    steps: [
      '1. Open app',
      '2. Select different moods',
      '3. Check analytics tracking',
      '4. Verify data persistence'
    ]
  },
  {
    name: 'Meditation Test', 
    description: 'Test meditation features',
    steps: [
      '1. Navigate to Meditation screen',
      '2. Start a meditation session',
      '3. Test pause/resume',
      '4. Complete session and check stats'
    ]
  },
  {
    name: 'Ad Integration Test',
    description: 'Test ad display and monetization',
    steps: [
      '1. Check banner ad display',
      '2. Test ad rotation',
      '3. Test ad click tracking',
      '4. Verify revenue calculation'
    ]
  },
  {
    name: 'Premium Features Test',
    description: 'Test premium subscription flow',
    steps: [
      '1. Try premium feature as free user',
      '2. Show upgrade prompt',
      '3. Test subscription flow',
      '4. Verify premium access'
    ]
  },
  {
    name: 'Notifications Test',
    description: 'Test push notifications',
    steps: [
      '1. Schedule test notification',
      '2. Test notification display',
      '3. Test notification actions',
      '4. Verify notification settings'
    ]
  }
];

console.log('\n📋 Available Test Scenarios:');
testScenarios.forEach((scenario, index) => {
  console.log(`\n${index + 1}. ${scenario.name}`);
  console.log(`   ${scenario.description}`);
  scenario.steps.forEach(step => console.log(`   ${step}`));
});

console.log('\n🚀 To run tests:');
console.log('npm run android  # Start Android app');
console.log('npm run ios      # Start iOS app');
console.log('\n💡 Test Tips:');
console.log('• Use test user: <EMAIL>');
console.log('• All ads are test ads (safe to click)');
console.log('• Analytics are logged to console');
console.log('• Fast mode enabled (quick timers)');
console.log('• Mock data preloaded');
EOF

# 6. Update package.json with test scripts
log_info "Adding test scripts to package.json..."

# Create backup
cp package.json package.json.backup

# Add test scripts using Node.js
node -e "
const fs = require('fs');
const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));

pkg.scripts = {
  ...pkg.scripts,
  'test:setup': 'node run-tests.js',
  'test:android': 'npm run android -- --mode=test',
  'test:ios': 'npm run ios -- --mode=test',
  'test:clean': 'rm -rf test-data/ test-config.js run-tests.js',
  'demo': 'echo \"🧠 Starting MindFlow Demo...\" && npm run android'
};

fs.writeFileSync('package.json', JSON.stringify(pkg, null, 2));
console.log('✅ Test scripts added to package.json');
"

# 7. Create quick demo script
cat > demo.sh << 'EOF'
#!/bin/bash

echo "🧠 MindFlow Demo Launcher"
echo "========================"
echo ""
echo "🎯 Demo Features:"
echo "• Mood tracking with 5 different moods"
echo "• Guided meditation sessions"
echo "• Smart ad integration (test ads)"
echo "• Progress analytics"
echo "• Premium features preview"
echo "• Push notifications"
echo ""
echo "📱 Starting Android app..."
echo "   (Make sure you have Android emulator running)"
echo ""

# Start Metro bundler in background
npm start &
METRO_PID=$!

# Wait a bit for Metro to start
sleep 5

# Start Android app
npm run android

# Cleanup on exit
trap "kill $METRO_PID 2>/dev/null" EXIT
EOF

chmod +x demo.sh

# 8. Create test checklist
cat > TEST_CHECKLIST.md << 'EOF'
# 🧠 MindFlow - Test Checklist

## 📱 **Quick Start**
```bash
./demo.sh  # Spustí demo aplikaci
```

## ✅ **Test Scenarios**

### **1. Mood Tracking Test**
- [ ] Otevři aplikaci
- [ ] Klikni na různé nálady (🤩😊😐😔😢)
- [ ] Zkontroluj, že se nálada uloží
- [ ] Ověř analytics v konzoli
- [ ] Zkontroluj streak counter

### **2. Ad Integration Test**
- [ ] Zkontroluj banner reklamu nahoře
- [ ] Počkej 5 sekund na rotaci reklamy
- [ ] Klikni na reklamu (test ad - bezpečné)
- [ ] Ověř revenue tracking v konzoli
- [ ] Zkontroluj různé typy reklam

### **3. Navigation Test**
- [ ] Přepni mezi všemi 5 taby
- [ ] Zkontroluj smooth animace
- [ ] Ověř, že se obsah načítá
- [ ] Test zpět tlačítka

### **4. Meditation Test**
- [ ] Jdi na Meditation tab
- [ ] Spusť quick meditation
- [ ] Test pause/resume (pokud implementováno)
- [ ] Zkontroluj progress update

### **5. Premium Features Test**
- [ ] Zkus přístup k premium funkci
- [ ] Ověř upgrade prompt
- [ ] Test subscription flow
- [ ] Zkontroluj premium badge

### **6. Notifications Test**
- [ ] Povol notifikace
- [ ] Zkontroluj scheduled reminders
- [ ] Test instant notification
- [ ] Ověř notification settings

### **7. Performance Test**
- [ ] Zkontroluj FPS counter
- [ ] Test memory usage
- [ ] Ověř smooth scrolling
- [ ] Test na různých velikostech obrazovky

### **8. Data Persistence Test**
- [ ] Nastav nějaká data
- [ ] Zavři a znovu otevři app
- [ ] Ověř, že data zůstala
- [ ] Test export/import dat

## 🐛 **Bug Reporting**
Pokud najdeš bug:
1. Zapiš kroky k reprodukci
2. Udělej screenshot
3. Zkontroluj console logy
4. Zapiš device info

## 📊 **Analytics Monitoring**
Sleduj v konzoli:
- Event tracking
- Ad impressions/clicks
- Revenue calculation
- User behavior

## 💡 **Test Tips**
- Všechny reklamy jsou testovací (bezpečné klikat)
- Analytics se logují do konzole
- Fast mode = rychlejší timers pro testování
- Mock data jsou přednastavená
- Test user: <EMAIL>

## 🎯 **Success Criteria**
- [ ] Aplikace se spustí bez crashů
- [ ] Všechny funkce fungují
- [ ] Reklamy se zobrazují a rotují
- [ ] Analytics trackují eventy
- [ ] Data se ukládají správně
- [ ] UI je responsive
- [ ] Performance je dobrá (>30 FPS)
EOF

# 9. Final setup
log_info "Creating Android test build..."

# Check if Android directory exists
if [ -d "android" ]; then
    log_info "Android project found, preparing test build..."
    
    # Create debug keystore if it doesn't exist
    if [ ! -f "android/app/debug.keystore" ]; then
        log_info "Creating debug keystore..."
        keytool -genkey -v -keystore android/app/debug.keystore -storepass android -alias androiddebugkey -keypass android -keyalg RSA -keysize 2048 -validity 10000 -dname "CN=Android Debug,O=Android,C=US" 2>/dev/null || true
    fi
else
    log_warning "Android directory not found. You may need to run 'npx react-native init' first."
fi

# 10. Success message
log_success "Test setup completed!"

echo ""
echo "🎉 MindFlow Test Environment Ready!"
echo "=================================="
echo ""
echo "🚀 Quick Start:"
echo "   ./demo.sh                    # Start demo app"
echo "   npm run test:setup           # Show test scenarios"
echo "   npm run android              # Start Android app"
echo ""
echo "📋 Test Files Created:"
echo "   • test-data/mock-users.json  # Test data"
echo "   • test-config.js             # Test configuration"
echo "   • run-tests.js               # Test runner"
echo "   • demo.sh                    # Demo launcher"
echo "   • TEST_CHECKLIST.md          # Test checklist"
echo ""
echo "💡 Test Features:"
echo "   ✅ Mock user data preloaded"
echo "   ✅ Test ads (safe to click)"
echo "   ✅ Analytics logging"
echo "   ✅ Fast mode timers"
echo "   ✅ Demo scenarios"
echo ""
echo "🎯 Next Steps:"
echo "   1. Run: ./demo.sh"
echo "   2. Follow TEST_CHECKLIST.md"
echo "   3. Test all features"
echo "   4. Report any issues"
echo ""
echo "Happy testing! 🧠✨"
EOF
