import AsyncStorage from '@react-native-async-storage/async-storage';

class StorageService {
  constructor() {
    this.keys = {
      USER_DATA: 'user_data',
      MOOD_HISTORY: 'mindflow_mood_history',
      TODAY_MOOD: 'mindflow_mood_today',
      MEDITATION_COUNT: 'mindflow_meditation_count',
      STREAK_DAYS: 'mindflow_streak_days',
      LAST_ACTIVITY: 'mindflow_last_activity',
      SETTINGS: 'mindflow_settings',
      PREMIUM_STATUS: 'mindflow_premium',
      ONBOARDING_COMPLETED: 'mindflow_onboarding',
    };
  }

  // User Data Management
  async getUserData() {
    try {
      const userData = await AsyncStorage.getItem(this.keys.USER_DATA);
      return userData ? JSON.parse(userData) : this.getDefaultUserData();
    } catch (error) {
      console.error('Error getting user data:', error);
      return this.getDefaultUserData();
    }
  }

  async saveUserData(userData) {
    try {
      await AsyncStorage.setItem(this.keys.USER_DATA, JSON.stringify(userData));
      return true;
    } catch (error) {
      console.error('Error saving user data:', error);
      return false;
    }
  }

  getDefaultUserData() {
    return {
      name: '',
      age: null,
      joinDate: new Date().toISOString(),
      streakDays: 0,
      totalMeditations: 0,
      totalMoodEntries: 0,
      averageMood: 5,
      preferredMeditationTime: 10,
      notificationsEnabled: true,
      darkMode: false,
      language: 'cs',
    };
  }

  // Mood Tracking
  async getTodayMood() {
    try {
      const today = new Date().toDateString();
      const moodData = await AsyncStorage.getItem(this.keys.TODAY_MOOD);
      
      if (moodData) {
        const parsed = JSON.parse(moodData);
        if (parsed.date === today) {
          return parsed.mood;
        }
      }
      return null;
    } catch (error) {
      console.error('Error getting today mood:', error);
      return null;
    }
  }

  async saveTodayMood(mood) {
    try {
      const today = new Date().toDateString();
      const moodData = {
        mood,
        date: today,
        timestamp: Date.now(),
      };
      
      await AsyncStorage.setItem(this.keys.TODAY_MOOD, JSON.stringify(moodData));
      
      // Přidej do historie
      await this.addMoodToHistory(mood);
      
      return true;
    } catch (error) {
      console.error('Error saving today mood:', error);
      return false;
    }
  }

  async addMoodToHistory(mood) {
    try {
      const history = await this.getMoodHistory();
      const today = new Date().toDateString();
      
      // Zkontroluj, jestli už není dnešní nálada v historii
      const existingIndex = history.findIndex(entry => entry.date === today);
      
      const newEntry = {
        mood,
        date: today,
        timestamp: Date.now(),
      };
      
      if (existingIndex >= 0) {
        // Aktualizuj existující záznam
        history[existingIndex] = newEntry;
      } else {
        // Přidej nový záznam
        history.push(newEntry);
      }
      
      // Zachovej pouze posledních 90 dní
      const sortedHistory = history
        .sort((a, b) => b.timestamp - a.timestamp)
        .slice(0, 90);
      
      await AsyncStorage.setItem(this.keys.MOOD_HISTORY, JSON.stringify(sortedHistory));
      
      // Aktualizuj uživatelská data
      const userData = await this.getUserData();
      userData.totalMoodEntries = sortedHistory.length;
      userData.averageMood = this.calculateAverageMood(sortedHistory);
      await this.saveUserData(userData);
      
      return true;
    } catch (error) {
      console.error('Error adding mood to history:', error);
      return false;
    }
  }

  async getMoodHistory() {
    try {
      const history = await AsyncStorage.getItem(this.keys.MOOD_HISTORY);
      return history ? JSON.parse(history) : [];
    } catch (error) {
      console.error('Error getting mood history:', error);
      return [];
    }
  }

  calculateAverageMood(history) {
    if (!history || history.length === 0) return 5;
    
    const moodValues = {
      amazing: 10,
      good: 8,
      okay: 6,
      bad: 4,
      terrible: 2,
    };
    
    const total = history.reduce((sum, entry) => {
      return sum + (moodValues[entry.mood] || 6);
    }, 0);
    
    return total / history.length;
  }

  // Streak Management
  async updateStreak() {
    try {
      const today = new Date().toDateString();
      const lastActivity = await AsyncStorage.getItem(this.keys.LAST_ACTIVITY);
      const currentStreak = await this.getStreakDays();
      
      if (lastActivity === today) {
        // Už dnes byla aktivita
        return currentStreak;
      }
      
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      const yesterdayString = yesterday.toDateString();
      
      let newStreak;
      if (lastActivity === yesterdayString) {
        // Pokračování v streak
        newStreak = currentStreak + 1;
      } else if (!lastActivity) {
        // První den
        newStreak = 1;
      } else {
        // Přerušení streak
        newStreak = 1;
      }
      
      await AsyncStorage.setItem(this.keys.STREAK_DAYS, newStreak.toString());
      await AsyncStorage.setItem(this.keys.LAST_ACTIVITY, today);
      
      // Aktualizuj uživatelská data
      const userData = await this.getUserData();
      userData.streakDays = newStreak;
      await this.saveUserData(userData);
      
      return newStreak;
    } catch (error) {
      console.error('Error updating streak:', error);
      return 0;
    }
  }

  async getStreakDays() {
    try {
      const streak = await AsyncStorage.getItem(this.keys.STREAK_DAYS);
      return streak ? parseInt(streak, 10) : 0;
    } catch (error) {
      console.error('Error getting streak days:', error);
      return 0;
    }
  }

  // Meditation Tracking
  async incrementMeditationCount() {
    try {
      const current = await this.getMeditationCount();
      const newCount = current + 1;
      
      await AsyncStorage.setItem(this.keys.MEDITATION_COUNT, newCount.toString());
      
      // Aktualizuj streak
      await this.updateStreak();
      
      // Aktualizuj uživatelská data
      const userData = await this.getUserData();
      userData.totalMeditations = newCount;
      await this.saveUserData(userData);
      
      return newCount;
    } catch (error) {
      console.error('Error incrementing meditation count:', error);
      return 0;
    }
  }

  async getMeditationCount() {
    try {
      const count = await AsyncStorage.getItem(this.keys.MEDITATION_COUNT);
      return count ? parseInt(count, 10) : 0;
    } catch (error) {
      console.error('Error getting meditation count:', error);
      return 0;
    }
  }

  // Settings Management
  async getSettings() {
    try {
      const settings = await AsyncStorage.getItem(this.keys.SETTINGS);
      return settings ? JSON.parse(settings) : this.getDefaultSettings();
    } catch (error) {
      console.error('Error getting settings:', error);
      return this.getDefaultSettings();
    }
  }

  async saveSettings(settings) {
    try {
      await AsyncStorage.setItem(this.keys.SETTINGS, JSON.stringify(settings));
      return true;
    } catch (error) {
      console.error('Error saving settings:', error);
      return false;
    }
  }

  getDefaultSettings() {
    return {
      notificationsEnabled: true,
      dailyReminderTime: '09:00',
      eveningReminderTime: '21:00',
      soundEnabled: true,
      vibrationEnabled: true,
      darkMode: false,
      language: 'cs',
      meditationDuration: 10,
      autoPlayNext: false,
      downloadOnWifi: true,
    };
  }

  // Premium Status
  async isPremiumUser() {
    try {
      const premium = await AsyncStorage.getItem(this.keys.PREMIUM_STATUS);
      return premium === 'true';
    } catch (error) {
      console.error('Error checking premium status:', error);
      return false;
    }
  }

  async setPremiumStatus(isPremium) {
    try {
      await AsyncStorage.setItem(this.keys.PREMIUM_STATUS, isPremium.toString());
      return true;
    } catch (error) {
      console.error('Error setting premium status:', error);
      return false;
    }
  }

  // Onboarding
  async isOnboardingCompleted() {
    try {
      const completed = await AsyncStorage.getItem(this.keys.ONBOARDING_COMPLETED);
      return completed === 'true';
    } catch (error) {
      console.error('Error checking onboarding status:', error);
      return false;
    }
  }

  async setOnboardingCompleted() {
    try {
      await AsyncStorage.setItem(this.keys.ONBOARDING_COMPLETED, 'true');
      return true;
    } catch (error) {
      console.error('Error setting onboarding completed:', error);
      return false;
    }
  }

  // Data Export/Import (pro GDPR compliance)
  async exportUserData() {
    try {
      const userData = await this.getUserData();
      const moodHistory = await this.getMoodHistory();
      const settings = await this.getSettings();
      const meditationCount = await this.getMeditationCount();
      const streakDays = await this.getStreakDays();
      
      const exportData = {
        userData,
        moodHistory,
        settings,
        meditationCount,
        streakDays,
        exportDate: new Date().toISOString(),
        version: '1.0.0',
      };
      
      return JSON.stringify(exportData, null, 2);
    } catch (error) {
      console.error('Error exporting user data:', error);
      return null;
    }
  }

  async importUserData(importDataString) {
    try {
      const importData = JSON.parse(importDataString);
      
      if (importData.userData) {
        await this.saveUserData(importData.userData);
      }
      
      if (importData.moodHistory) {
        await AsyncStorage.setItem(this.keys.MOOD_HISTORY, JSON.stringify(importData.moodHistory));
      }
      
      if (importData.settings) {
        await this.saveSettings(importData.settings);
      }
      
      if (importData.meditationCount) {
        await AsyncStorage.setItem(this.keys.MEDITATION_COUNT, importData.meditationCount.toString());
      }
      
      if (importData.streakDays) {
        await AsyncStorage.setItem(this.keys.STREAK_DAYS, importData.streakDays.toString());
      }
      
      return true;
    } catch (error) {
      console.error('Error importing user data:', error);
      return false;
    }
  }

  // Clear all data (pro reset nebo odinstalaci)
  async clearAllData() {
    try {
      const keys = Object.values(this.keys);
      await AsyncStorage.multiRemove(keys);
      return true;
    } catch (error) {
      console.error('Error clearing all data:', error);
      return false;
    }
  }

  // Weekly/Monthly stats
  async getWeeklyStats() {
    try {
      const history = await this.getMoodHistory();
      const oneWeekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
      
      const weeklyMoods = history.filter(entry => entry.timestamp > oneWeekAgo);
      
      return {
        totalEntries: weeklyMoods.length,
        averageMood: this.calculateAverageMood(weeklyMoods),
        moodDistribution: this.getMoodDistribution(weeklyMoods),
        streak: await this.getStreakDays(),
        meditations: await this.getMeditationCount(), // Celkový počet
      };
    } catch (error) {
      console.error('Error getting weekly stats:', error);
      return null;
    }
  }

  getMoodDistribution(moodEntries) {
    const distribution = {
      amazing: 0,
      good: 0,
      okay: 0,
      bad: 0,
      terrible: 0,
    };
    
    moodEntries.forEach(entry => {
      if (distribution.hasOwnProperty(entry.mood)) {
        distribution[entry.mood]++;
      }
    });
    
    return distribution;
  }
}

export default new StorageService();
