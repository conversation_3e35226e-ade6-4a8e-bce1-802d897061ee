// 3D Universe Simulator - Brutální vesmírná simulace
class Universe3D {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;

        // Fyzikální objekty
        this.objects = [];
        this.trails = new Map();
        this.explosions = [];

        // Fyzikální konstanty
        this.G = 6.67430e-11;
        this.c = 299792458;
        this.timeScale = 1.0;
        this.gravityMultiplier = 1.0;

        // Simulační stav
        this.isRunning = true;
        this.enableTrails = true;
        this.enableCollisions = true;
        this.enableRelativity = false;
        this.simulationTime = 0;

        // Herní stav
        this.currentMode = 'planet';
        this.selectedMass = 1e24;
        this.selectedVelocity = 0;
        this.selectedSize = 1.0;

        // Performance
        this.lastTime = 0;
        this.fps = 60;
        this.frameCount = 0;
        this.lastFpsUpdate = 0;

        // Raycaster pro interakci
        this.raycaster = new THREE.Raycaster();
        this.mouse = new THREE.Vector2();
        this.hoveredObject = null;

        this.init();
    }

    async init() {
        try {
            await this.setupScene();
            await this.setupLighting();
            await this.setupControls();
            await this.setupEventListeners();
            await this.createStarField();

            // Skryj loading a zobraz UI
            document.getElementById('loading').style.display = 'none';
            document.getElementById('hud').style.display = 'block';

            // Spusť animační smyčku
            this.animate();

            // Vytvoř základní scénář
            setTimeout(() => {
                this.createSolarSystem();
            }, 1000);

            console.log('🌌 3D Universe Simulator inicializován!');

        } catch (error) {
            console.error('Chyba při inicializaci:', error);
            document.getElementById('loading').textContent = '❌ Chyba při načítání!';
        }
    }

    async setupScene() {
        // Scéna
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x000011);
        this.scene.fog = new THREE.Fog(0x000011, 1000, 10000);

        // Kamera
        const container = document.getElementById('container');
        this.camera = new THREE.PerspectiveCamera(
            75,
            container.clientWidth / container.clientHeight,
            0.1,
            100000
        );
        this.camera.position.set(0, 50, 100);

        // Renderer
        this.renderer = new THREE.WebGLRenderer({
            canvas: document.getElementById('canvas3d'),
            antialias: true,
            alpha: true
        });
        this.renderer.setSize(container.clientWidth, container.clientHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 0.8;
    }

    async setupLighting() {
        // Ambientní světlo
        const ambientLight = new THREE.AmbientLight(0x404040, 0.2);
        this.scene.add(ambientLight);

        // Hlavní světlo (simuluje vzdálené hvězdy)
        const mainLight = new THREE.DirectionalLight(0xffffff, 0.5);
        mainLight.position.set(100, 100, 50);
        mainLight.castShadow = true;
        mainLight.shadow.mapSize.width = 2048;
        mainLight.shadow.mapSize.height = 2048;
        mainLight.shadow.camera.near = 0.5;
        mainLight.shadow.camera.far = 500;
        mainLight.shadow.camera.left = -100;
        mainLight.shadow.camera.right = 100;
        mainLight.shadow.camera.top = 100;
        mainLight.shadow.camera.bottom = -100;
        this.scene.add(mainLight);
    }

    async setupControls() {
        // Orbit controls pro 3D navigaci
        this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        this.controls.screenSpacePanning = false;
        this.controls.minDistance = 1;
        this.controls.maxDistance = 10000;
        this.controls.maxPolarAngle = Math.PI;

        // Rychlejší pohyb
        this.controls.rotateSpeed = 0.5;
        this.controls.zoomSpeed = 1.2;
        this.controls.panSpeed = 0.8;

        // Změna ovládání - pravé tlačítko pro rotaci
        this.controls.mouseButtons = {
            LEFT: null, // Levé tlačítko nebude rotovat
            MIDDLE: THREE.MOUSE.DOLLY,
            RIGHT: THREE.MOUSE.ROTATE // Pravé tlačítko pro rotaci
        };
    }

    async setupEventListeners() {
        // Resize
        window.addEventListener('resize', () => this.onWindowResize());

        // Mouse events
        this.renderer.domElement.addEventListener('click', (e) => this.onMouseClick(e));
        this.renderer.domElement.addEventListener('mousemove', (e) => this.onMouseMove(e));

        // Keyboard
        document.addEventListener('keydown', (e) => this.onKeyDown(e));
    }

    async createStarField() {
        // Vytvoř tisíce hvězd na pozadí
        const starGeometry = new THREE.BufferGeometry();
        const starCount = 10000;
        const positions = new Float32Array(starCount * 3);
        const colors = new Float32Array(starCount * 3);

        for (let i = 0; i < starCount; i++) {
            // Náhodné pozice ve sféře
            const radius = 5000 + Math.random() * 5000;
            const theta = Math.random() * Math.PI * 2;
            const phi = Math.acos(2 * Math.random() - 1);

            positions[i * 3] = radius * Math.sin(phi) * Math.cos(theta);
            positions[i * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
            positions[i * 3 + 2] = radius * Math.cos(phi);

            // Různé barvy hvězd
            const color = new THREE.Color();
            const hue = Math.random() * 0.3 + 0.5; // Modré až žluté
            color.setHSL(hue, 0.8, 0.8);
            colors[i * 3] = color.r;
            colors[i * 3 + 1] = color.g;
            colors[i * 3 + 2] = color.b;
        }

        starGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        starGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

        const starMaterial = new THREE.PointsMaterial({
            size: 2,
            vertexColors: true,
            transparent: true,
            opacity: 0.8
        });

        const stars = new THREE.Points(starGeometry, starMaterial);
        this.scene.add(stars);
    }

    createObject(type, position, mass, velocity = new THREE.Vector3()) {
        const obj = {
            id: Date.now() + Math.random(),
            type: type,
            mass: mass,
            position: position.clone(),
            velocity: velocity.clone(),
            force: new THREE.Vector3(),

            // 3D mesh
            mesh: null,

            // Fyzikální vlastnosti
            radius: this.calculateRadius(mass, type),
            temperature: this.calculateTemperature(mass, type),
            luminosity: this.calculateLuminosity(mass, type),
            age: 0,

            // Vizuální efekty
            glow: null,
            trail: [],

            // Stav
            isDestroyed: false
        };

        // Vytvoř 3D mesh
        this.createObjectMesh(obj);

        // Přidej do scény
        this.scene.add(obj.mesh);
        if (obj.glow) this.scene.add(obj.glow);

        this.objects.push(obj);
        return obj;
    }

    createObjectMesh(obj) {
        let geometry, material;

        switch(obj.type) {
            case 'planet':
                geometry = new THREE.SphereGeometry(obj.radius * this.selectedSize, 32, 32);
                material = new THREE.MeshPhongMaterial({
                    color: this.getPlanetColor(),
                    shininess: 30,
                    transparent: true,
                    opacity: 0.9
                });

                // Přidej texturu (procedurální)
                this.addPlanetTexture(material);
                break;

            case 'star':
                geometry = new THREE.SphereGeometry(obj.radius * this.selectedSize, 32, 32);
                material = new THREE.MeshBasicMaterial({
                    color: this.getStarColor(obj.temperature),
                    transparent: true,
                    opacity: 0.8
                });

                // Glow efekt pro hvězdy
                this.createStarGlow(obj);
                break;

            case 'blackhole':
                geometry = new THREE.SphereGeometry(obj.radius * this.selectedSize, 32, 32);
                material = new THREE.MeshBasicMaterial({
                    color: 0x000000,
                    transparent: true,
                    opacity: 0.9
                });

                // Accretion disk
                this.createAccretionDisk(obj);
                break;

            case 'asteroid':
                geometry = new THREE.DodecahedronGeometry(obj.radius * this.selectedSize, 1);
                material = new THREE.MeshPhongMaterial({
                    color: 0x8b4513,
                    roughness: 0.8,
                    metalness: 0.2
                });
                break;

            case 'ship':
                geometry = new THREE.ConeGeometry(obj.radius * this.selectedSize, obj.radius * 3 * this.selectedSize, 8);
                material = new THREE.MeshPhongMaterial({
                    color: 0x00ff00,
                    emissive: 0x002200,
                    shininess: 100
                });
                break;

            default:
                geometry = new THREE.SphereGeometry(obj.radius * this.selectedSize, 16, 16);
                material = new THREE.MeshPhongMaterial({ color: 0xffffff });
        }

        obj.mesh = new THREE.Mesh(geometry, material);
        obj.mesh.position.copy(obj.position);
        obj.mesh.castShadow = true;
        obj.mesh.receiveShadow = true;
        obj.mesh.userData = obj; // Reference zpět na objekt
    }

    getPlanetColor() {
        const colors = [0x4a90e2, 0x8b4513, 0xff6b6b, 0x4ecdc4, 0xffa726, 0x9c27b0];
        return colors[Math.floor(Math.random() * colors.length)];
    }

    getStarColor(temperature) {
        if (temperature > 30000) return 0x9bb0ff; // Modrá
        if (temperature > 10000) return 0xaabfff; // Bílo-modrá
        if (temperature > 7500) return 0xcad7ff;  // Bílá
        if (temperature > 6000) return 0xfff4ea;  // Žluto-bílá
        if (temperature > 5200) return 0xfff4ea;  // Žlutá
        if (temperature > 3700) return 0xffd2a1;  // Oranžová
        return 0xffad51; // Červená
    }

    createStarGlow(obj) {
        const glowGeometry = new THREE.SphereGeometry(obj.radius * this.selectedSize * 2, 16, 16);
        const glowMaterial = new THREE.MeshBasicMaterial({
            color: this.getStarColor(obj.temperature),
            transparent: true,
            opacity: 0.3,
            side: THREE.BackSide
        });

        obj.glow = new THREE.Mesh(glowGeometry, glowMaterial);
        obj.glow.position.copy(obj.position);
    }

    createAccretionDisk(obj) {
        const diskGeometry = new THREE.RingGeometry(
            obj.radius * this.selectedSize * 2,
            obj.radius * this.selectedSize * 5,
            32
        );
        const diskMaterial = new THREE.MeshBasicMaterial({
            color: 0xff6600,
            transparent: true,
            opacity: 0.6,
            side: THREE.DoubleSide
        });

        const disk = new THREE.Mesh(diskGeometry, diskMaterial);
        disk.rotation.x = Math.PI / 2;
        obj.mesh.add(disk);
    }

    addPlanetTexture(material) {
        // Procedurální textura planety
        const canvas = document.createElement('canvas');
        canvas.width = 512;
        canvas.height = 256;
        const ctx = canvas.getContext('2d');

        // Gradient pro planetu
        const gradient = ctx.createLinearGradient(0, 0, 512, 256);
        gradient.addColorStop(0, '#4a90e2');
        gradient.addColorStop(0.3, '#2c5aa0');
        gradient.addColorStop(0.7, '#1e3a8a');
        gradient.addColorStop(1, '#1e40af');

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 512, 256);

        // Přidej "kontinenty"
        ctx.fillStyle = '#8b4513';
        for (let i = 0; i < 20; i++) {
            const x = Math.random() * 512;
            const y = Math.random() * 256;
            const size = Math.random() * 50 + 10;
            ctx.beginPath();
            ctx.arc(x, y, size, 0, Math.PI * 2);
            ctx.fill();
        }

        const texture = new THREE.CanvasTexture(canvas);
        material.map = texture;
    }

    calculateRadius(mass, type) {
        switch(type) {
            case 'star':
                return Math.pow(mass / 1.989e30, 0.8) * 2; // Škálováno pro vizualizaci
            case 'planet':
                return Math.pow(mass / 5.972e24, 0.27) * 1;
            case 'blackhole':
                return (2 * this.G * mass) / (this.c * this.c) * 1e10; // Škálováno
            case 'asteroid':
                return Math.pow(mass / 1e15, 1/3) * 0.5;
            case 'ship':
                return 0.1;
            default:
                return Math.pow(mass / 1e20, 1/3);
        }
    }

    calculateTemperature(mass, type) {
        switch(type) {
            case 'star':
                return 5778 * Math.pow(mass / 1.989e30, 0.5);
            case 'planet':
                return 288;
            case 'blackhole':
                return (6.17e-8) / mass; // Hawkingova teplota (zjednodušeno)
            default:
                return 2.7;
        }
    }

    calculateLuminosity(mass, type) {
        if (type === 'star') {
            return 3.828e26 * Math.pow(mass / 1.989e30, 3.5);
        }
        return 0;
    }

    // Fyzikální update
    updatePhysics(deltaTime) {
        if (!this.isRunning) return;

        const dt = deltaTime * this.timeScale;
        this.simulationTime += dt;

        // Výpočet gravitačních sil
        this.calculateGravitationalForces();

        // Aktualizace pozic a rychlostí
        this.updatePositions(dt);

        // Kolize
        if (this.enableCollisions) {
            this.checkCollisions();
        }

        // Stopy
        if (this.enableTrails) {
            this.updateTrails();
        }

        // Evoluce objektů
        this.evolveObjects(dt);

        // Aktualizace 3D meshů
        this.updateMeshes();

        // Vyčištění zničených objektů
        this.cleanupDestroyed();
    }

    calculateGravitationalForces() {
        // Reset sil
        this.objects.forEach(obj => {
            obj.force.set(0, 0, 0);
        });

        // N-body gravitace
        for (let i = 0; i < this.objects.length; i++) {
            for (let j = i + 1; j < this.objects.length; j++) {
                const obj1 = this.objects[i];
                const obj2 = this.objects[j];

                if (obj1.isDestroyed || obj2.isDestroyed) continue;

                const distance = obj1.position.distanceTo(obj2.position);
                if (distance < obj1.radius + obj2.radius) continue; // Kolize

                // F = G * m1 * m2 / r²
                const forceMagnitude = this.G * obj1.mass * obj2.mass / (distance * distance) * this.gravityMultiplier;

                // Směrový vektor
                const direction = new THREE.Vector3()
                    .subVectors(obj2.position, obj1.position)
                    .normalize();

                const force = direction.multiplyScalar(forceMagnitude);

                obj1.force.add(force);
                obj2.force.sub(force);
            }
        }
    }

    updatePositions(dt) {
        this.objects.forEach(obj => {
            if (obj.isDestroyed) return;

            // a = F/m
            const acceleration = obj.force.clone().divideScalar(obj.mass);

            // Verlet integrace
            obj.velocity.add(acceleration.multiplyScalar(dt));
            obj.position.add(obj.velocity.clone().multiplyScalar(dt));

            // Stárnutí
            obj.age += dt;
        });
    }

    checkCollisions() {
        for (let i = 0; i < this.objects.length; i++) {
            for (let j = i + 1; j < this.objects.length; j++) {
                const obj1 = this.objects[i];
                const obj2 = this.objects[j];

                if (obj1.isDestroyed || obj2.isDestroyed) continue;

                const distance = obj1.position.distanceTo(obj2.position);
                if (distance < obj1.radius + obj2.radius) {
                    this.handleCollision(obj1, obj2);
                }
            }
        }
    }

    handleCollision(obj1, obj2) {
        // Určí vítěze podle hmotnosti
        const winner = obj1.mass > obj2.mass ? obj1 : obj2;
        const loser = obj1.mass > obj2.mass ? obj2 : obj1;

        // Zachování hybnosti
        const totalMass = winner.mass + loser.mass;
        const newVelocity = new THREE.Vector3()
            .addVectors(
                winner.velocity.clone().multiplyScalar(winner.mass),
                loser.velocity.clone().multiplyScalar(loser.mass)
            )
            .divideScalar(totalMass);

        // Aktualizace vítěze
        winner.mass = totalMass;
        winner.velocity.copy(newVelocity);
        winner.radius = this.calculateRadius(winner.mass, winner.type);

        // Vytvoř nový mesh pro změněnou velikost
        this.scene.remove(winner.mesh);
        if (winner.glow) this.scene.remove(winner.glow);
        this.createObjectMesh(winner);
        this.scene.add(winner.mesh);
        if (winner.glow) this.scene.add(winner.glow);

        // Zničení poraženého
        loser.isDestroyed = true;

        // Exploze
        this.createExplosion(loser.position, loser.mass);

        console.log(`💥 Kolize: ${winner.type} pohltil ${loser.type}`);
    }

    createExplosion(position, mass) {
        const particleCount = Math.min(100, Math.sqrt(mass / 1e20));

        for (let i = 0; i < particleCount; i++) {
            const particle = {
                position: position.clone(),
                velocity: new THREE.Vector3(
                    (Math.random() - 0.5) * 20,
                    (Math.random() - 0.5) * 20,
                    (Math.random() - 0.5) * 20
                ),
                life: 1.0,
                maxLife: 2.0,
                mesh: null
            };

            // Vytvoř particle mesh
            const geometry = new THREE.SphereGeometry(0.1, 8, 8);
            const material = new THREE.MeshBasicMaterial({
                color: 0xff6600,
                transparent: true,
                opacity: 1.0
            });

            particle.mesh = new THREE.Mesh(geometry, material);
            particle.mesh.position.copy(particle.position);
            this.scene.add(particle.mesh);

            this.explosions.push(particle);
        }
    }

    updateTrails() {
        this.objects.forEach(obj => {
            if (obj.isDestroyed) return;

            // Přidej nový bod do stopy
            obj.trail.push({
                position: obj.position.clone(),
                time: this.simulationTime
            });

            // Omeз délku stopy
            const maxTrailLength = 50;
            if (obj.trail.length > maxTrailLength) {
                obj.trail.shift();
            }

            // Odstraň staré body
            const maxAge = 10; // sekund
            obj.trail = obj.trail.filter(point =>
                this.simulationTime - point.time < maxAge
            );
        });
    }

    evolveObjects(dt) {
        this.objects.forEach(obj => {
            if (obj.isDestroyed) return;

            switch(obj.type) {
                case 'star':
                    this.evolveStar(obj, dt);
                    break;
                case 'blackhole':
                    this.evolveBlackHole(obj, dt);
                    break;
            }
        });

        // Aktualizace explozí
        this.explosions = this.explosions.filter(particle => {
            particle.life -= dt / particle.maxLife;

            if (particle.life > 0) {
                particle.position.add(particle.velocity.clone().multiplyScalar(dt));
                particle.mesh.position.copy(particle.position);
                particle.mesh.material.opacity = particle.life;
                particle.mesh.scale.setScalar(particle.life);
                return true;
            } else {
                this.scene.remove(particle.mesh);
                return false;
            }
        });
    }

    evolveStar(star, dt) {
        // Jaderná fúze
        const fusionRate = star.mass * 1e-18;
        star.mass -= fusionRate * dt;

        // Aktualizace teploty
        star.temperature = this.calculateTemperature(star.mass, 'star');

        // Změna barvy podle teploty
        if (star.mesh) {
            star.mesh.material.color.setHex(this.getStarColor(star.temperature));
        }

        // Smrt hvězdy
        if (star.mass < 0.08 * 1.989e30) {
            this.stellarDeath(star);
        }
    }

    stellarDeath(star) {
        const originalMass = star.mass;

        if (originalMass > 25 * 1.989e30) {
            // Supernova -> černá díra
            star.type = 'blackhole';
            star.mass = originalMass * 0.3;

            // Vytvoř nový mesh
            this.scene.remove(star.mesh);
            if (star.glow) this.scene.remove(star.glow);
            this.createObjectMesh(star);
            this.scene.add(star.mesh);

            // Supernova exploze
            this.createExplosion(star.position, originalMass * 0.7);

            console.log('💀 Hvězda se stala černou dírou!');
        }
    }

    evolveBlackHole(blackhole, dt) {
        // Hawkingovo vyzařování (zjednodušeno)
        const hawkingRate = 1e-20 / blackhole.mass;
        blackhole.mass -= hawkingRate * dt;

        if (blackhole.mass < 1e15) {
            blackhole.isDestroyed = true;
            this.createExplosion(blackhole.position, blackhole.mass);
        }
    }

    updateMeshes() {
        this.objects.forEach(obj => {
            if (obj.isDestroyed || !obj.mesh) return;

            // Aktualizace pozice
            obj.mesh.position.copy(obj.position);

            // Rotace
            obj.mesh.rotation.x += 0.01;
            obj.mesh.rotation.y += 0.02;

            // Glow efekt
            if (obj.glow) {
                obj.glow.position.copy(obj.position);
                obj.glow.rotation.x += 0.005;
                obj.glow.rotation.y += 0.01;
            }
        });
    }

    cleanupDestroyed() {
        this.objects = this.objects.filter(obj => {
            if (obj.isDestroyed) {
                this.scene.remove(obj.mesh);
                if (obj.glow) this.scene.remove(obj.glow);
                return false;
            }
            return true;
        });
    }

    // Interakce a události
    onMouseClick(event) {
        // Převod na normalized device coordinates
        const rect = this.renderer.domElement.getBoundingClientRect();
        this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

        // Raycasting
        this.raycaster.setFromCamera(this.mouse, this.camera);

        if (this.currentMode === 'delete') {
            // Smazání objektu
            const intersects = this.raycaster.intersectObjects(
                this.objects.map(obj => obj.mesh).filter(mesh => mesh)
            );

            if (intersects.length > 0) {
                const clickedObject = intersects[0].object.userData;
                if (clickedObject) {
                    clickedObject.isDestroyed = true;
                    console.log(`🗑️ Smazán ${clickedObject.type}`);
                }
            }
        } else {
            // Vytvoření nového objektu
            const distance = 50; // Vzdálenost od kamery
            const worldPosition = new THREE.Vector3();

            // Vypočítej pozici ve světě
            this.raycaster.ray.at(distance, worldPosition);

            // Náhodná počáteční rychlost
            const velocity = new THREE.Vector3(
                (Math.random() - 0.5) * this.selectedVelocity,
                (Math.random() - 0.5) * this.selectedVelocity,
                (Math.random() - 0.5) * this.selectedVelocity
            );

            this.createObject(this.currentMode, worldPosition, this.selectedMass, velocity);
            console.log(`✨ Vytvořen ${this.currentMode} na pozici ${worldPosition.x.toFixed(1)}, ${worldPosition.y.toFixed(1)}, ${worldPosition.z.toFixed(1)}`);
        }
    }

    onMouseMove(event) {
        // Aktualizace mouse pozice pro hover efekty
        const rect = this.renderer.domElement.getBoundingClientRect();
        this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

        // Raycasting pro hover
        this.raycaster.setFromCamera(this.mouse, this.camera);
        const intersects = this.raycaster.intersectObjects(
            this.objects.map(obj => obj.mesh).filter(mesh => mesh)
        );

        if (intersects.length > 0) {
            const hoveredObj = intersects[0].object.userData;
            if (hoveredObj !== this.hoveredObject) {
                this.hoveredObject = hoveredObj;
                this.showObjectInfo(hoveredObj, event);
            }
        } else {
            this.hoveredObject = null;
            this.hideObjectInfo();
        }
    }

    onKeyDown(event) {
        switch(event.key) {
            case ' ':
                event.preventDefault();
                this.togglePhysics();
                break;
            case '1': this.setMode('planet'); break;
            case '2': this.setMode('star'); break;
            case '3': this.setMode('asteroid'); break;
            case '4': this.setMode('blackhole'); break;
            case '5': this.setMode('ship'); break;
            case 'Delete':
                this.clearUniverse();
                break;
            case 'r':
            case 'R':
                this.resetCamera();
                break;
        }
    }

    onWindowResize() {
        const container = document.getElementById('container');
        this.camera.aspect = container.clientWidth / container.clientHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(container.clientWidth, container.clientHeight);
    }

    showObjectInfo(obj, event) {
        const infoDiv = document.getElementById('object-info');
        const content = `
            <strong>${obj.type.toUpperCase()}</strong><br>
            <strong>Hmotnost:</strong> ${this.formatMass(obj.mass)}<br>
            <strong>Poloměr:</strong> ${this.formatDistance(obj.radius)}<br>
            <strong>Rychlost:</strong> ${this.formatVelocity(obj.velocity.length())}<br>
            <strong>Teplota:</strong> ${obj.temperature.toFixed(1)} K<br>
            <strong>Věk:</strong> ${this.formatTime(obj.age)}<br>
            ${obj.luminosity > 0 ? `<strong>Svítivost:</strong> ${obj.luminosity.toExponential(2)} W<br>` : ''}
            <strong>Pozice:</strong> (${obj.position.x.toFixed(1)}, ${obj.position.y.toFixed(1)}, ${obj.position.z.toFixed(1)})
        `;

        document.getElementById('info-content').innerHTML = content;
        infoDiv.style.display = 'block';
        infoDiv.style.left = event.clientX + 15 + 'px';
        infoDiv.style.top = event.clientY + 15 + 'px';
    }

    hideObjectInfo() {
        document.getElementById('object-info').style.display = 'none';
    }

    // Předpřipravené scénáře
    createSolarSystem() {
        this.clearUniverse();

        // Slunce ve středu
        const sun = this.createObject('star', new THREE.Vector3(0, 0, 0), 1.989e30);

        // Planety s realistickými parametry
        const planets = [
            { name: 'Merkur', distance: 15, mass: 3.301e23, velocity: 8 },
            { name: 'Venuše', distance: 20, mass: 4.867e24, velocity: 7 },
            { name: 'Země', distance: 25, mass: 5.972e24, velocity: 6 },
            { name: 'Mars', distance: 35, mass: 6.417e23, velocity: 5 },
            { name: 'Jupiter', distance: 50, mass: 1.898e27, velocity: 3 },
            { name: 'Saturn', distance: 70, mass: 5.683e26, velocity: 2.5 },
            { name: 'Uran', distance: 90, mass: 8.681e25, velocity: 2 },
            { name: 'Neptun', distance: 110, mass: 1.024e26, velocity: 1.5 }
        ];

        planets.forEach(planetData => {
            const angle = Math.random() * Math.PI * 2;
            const position = new THREE.Vector3(
                Math.cos(angle) * planetData.distance,
                (Math.random() - 0.5) * 5, // Malá variace v Y
                Math.sin(angle) * planetData.distance
            );

            // Orbitální rychlost kolmo na poloměr
            const velocity = new THREE.Vector3(
                -Math.sin(angle) * planetData.velocity,
                0,
                Math.cos(angle) * planetData.velocity
            );

            this.createObject('planet', position, planetData.mass, velocity);
        });

        console.log('☀️ Sluneční soustava vytvořena!');
    }

    createBinarySystem() {
        this.clearUniverse();

        const separation = 40;
        const orbitalVelocity = 3;

        // Hvězda A
        this.createObject('star',
            new THREE.Vector3(-separation/2, 0, 0),
            1.989e30,
            new THREE.Vector3(0, 0, orbitalVelocity)
        );

        // Hvězda B
        this.createObject('star',
            new THREE.Vector3(separation/2, 0, 0),
            1.989e30,
            new THREE.Vector3(0, 0, -orbitalVelocity)
        );

        // Několik planet
        for (let i = 0; i < 5; i++) {
            const angle = Math.random() * Math.PI * 2;
            const distance = 80 + Math.random() * 40;
            const position = new THREE.Vector3(
                Math.cos(angle) * distance,
                (Math.random() - 0.5) * 10,
                Math.sin(angle) * distance
            );

            const velocity = new THREE.Vector3(
                -Math.sin(angle) * 1.5,
                0,
                Math.cos(angle) * 1.5
            );

            this.createObject('planet', position, (Math.random() * 5 + 1) * 5.972e24, velocity);
        }

        console.log('⭐⭐ Dvojhvězda vytvořena!');
    }

    createGalaxy() {
        this.clearUniverse();

        // Centrální černá díra
        this.createObject('blackhole', new THREE.Vector3(0, 0, 0), 4.1e36);

        // Spirální ramena
        const armCount = 4;
        const starsPerArm = 30;

        for (let arm = 0; arm < armCount; arm++) {
            for (let i = 0; i < starsPerArm; i++) {
                const t = i / starsPerArm * 3 * Math.PI; // 3 otáčky
                const r = (i / starsPerArm) * 150 + 20; // 20-170 jednotek

                const armAngle = (arm / armCount) * 2 * Math.PI;
                const spiralAngle = armAngle + t * 0.3;

                const position = new THREE.Vector3(
                    r * Math.cos(spiralAngle),
                    (Math.random() - 0.5) * 10,
                    r * Math.sin(spiralAngle)
                );

                // Orbitální rychlost
                const orbitalVel = Math.sqrt(50 / r); // Zjednodušeno
                const velocity = new THREE.Vector3(
                    -Math.sin(spiralAngle) * orbitalVel,
                    0,
                    Math.cos(spiralAngle) * orbitalVel
                );

                // Různé typy objektů
                const types = ['star', 'star', 'star', 'blackhole'];
                const type = types[Math.floor(Math.random() * types.length)];
                const mass = type === 'blackhole' ?
                    (Math.random() * 10 + 3) * 1.989e30 :
                    (Math.random() * 5 + 0.5) * 1.989e30;

                this.createObject(type, position, mass, velocity);
            }
        }

        console.log('🌌 Galaxie vytvořena!');
    }

    createAsteroidField() {
        this.clearUniverse();

        // Centrální hvězda
        this.createObject('star', new THREE.Vector3(0, 0, 0), 1.989e30);

        // Asteroidový pás
        for (let i = 0; i < 100; i++) {
            const angle = Math.random() * Math.PI * 2;
            const distance = 40 + Math.random() * 20; // 40-60 jednotek
            const height = (Math.random() - 0.5) * 5;

            const position = new THREE.Vector3(
                Math.cos(angle) * distance,
                height,
                Math.sin(angle) * distance
            );

            const orbitalVel = Math.sqrt(30 / distance);
            const velocity = new THREE.Vector3(
                -Math.sin(angle) * orbitalVel * (0.8 + Math.random() * 0.4),
                (Math.random() - 0.5) * 0.5,
                Math.cos(angle) * orbitalVel * (0.8 + Math.random() * 0.4)
            );

            const mass = Math.random() * 1e18 + 1e15; // Různé velikosti asteroidů
            this.createObject('asteroid', position, mass, velocity);
        }

        console.log('☄️ Asteroidové pole vytvořeno!');
    }

    // Animační smyčka
    animate() {
        requestAnimationFrame(() => this.animate());

        const currentTime = performance.now();
        const deltaTime = (currentTime - this.lastTime) / 1000;
        this.lastTime = currentTime;

        // FPS počítání
        this.frameCount++;
        if (currentTime - this.lastFpsUpdate > 1000) {
            this.fps = this.frameCount;
            this.frameCount = 0;
            this.lastFpsUpdate = currentTime;
        }

        // Aktualizace controls
        this.controls.update();

        // Fyzikální update
        this.updatePhysics(deltaTime);

        // Rendering
        this.renderer.render(this.scene, this.camera);

        // UI update
        this.updateUI();
    }

    updateUI() {
        // FPS a počet objektů
        document.getElementById('fps-display').textContent = this.fps;
        document.getElementById('object-count').textContent = this.objects.length;

        // Fyzikální statistiky
        document.getElementById('sim-time').textContent = this.formatTime(this.simulationTime);

        let totalMass = 0;
        let totalEnergy = 0;

        this.objects.forEach(obj => {
            if (!obj.isDestroyed) {
                totalMass += obj.mass;
                totalEnergy += 0.5 * obj.mass * obj.velocity.lengthSq(); // Kinetická energie
            }
        });

        document.getElementById('total-mass').textContent = this.formatMass(totalMass);
        document.getElementById('total-energy').textContent = this.formatEnergy(totalEnergy);
        document.getElementById('gravity-strength').textContent = (this.gravityMultiplier * 100).toFixed(0) + '%';
    }

    // Utility funkce pro formátování
    formatTime(seconds) {
        if (seconds < 60) return seconds.toFixed(2) + ' s';
        if (seconds < 3600) return (seconds / 60).toFixed(1) + ' min';
        if (seconds < 86400) return (seconds / 3600).toFixed(1) + ' h';
        if (seconds < 31536000) return (seconds / 86400).toFixed(1) + ' dní';
        return (seconds / 31536000).toFixed(1) + ' let';
    }

    formatMass(mass) {
        if (mass < 1e3) return mass.toFixed(2) + ' kg';
        if (mass < 1e6) return (mass / 1e3).toFixed(2) + ' t';
        if (mass < 1e30) return (mass / 1e24).toFixed(2) + ' Yg';
        return (mass / 1.989e30).toFixed(2) + ' M☉';
    }

    formatEnergy(energy) {
        if (energy < 1e3) return energy.toFixed(2) + ' J';
        if (energy < 1e6) return (energy / 1e3).toFixed(2) + ' kJ';
        if (energy < 1e9) return (energy / 1e6).toFixed(2) + ' MJ';
        if (energy < 1e12) return (energy / 1e9).toFixed(2) + ' GJ';
        return (energy / 1e12).toFixed(2) + ' TJ';
    }

    formatDistance(distance) {
        if (distance < 1e3) return distance.toFixed(0) + ' m';
        if (distance < 1e6) return (distance / 1e3).toFixed(1) + ' km';
        if (distance < 1.496e11) return (distance / 1e6).toFixed(1) + ' Mm';
        if (distance < 9.461e15) return (distance / 1.496e11).toFixed(2) + ' AU';
        return (distance / 9.461e15).toFixed(2) + ' ly';
    }

    formatVelocity(velocity) {
        if (velocity < 1e3) return velocity.toFixed(0) + ' m/s';
        if (velocity < 3e8) return (velocity / 1e3).toFixed(1) + ' km/s';
        return (velocity / 299792458).toFixed(3) + ' c';
    }

    // Herní funkce
    setMode(mode) {
        this.currentMode = mode;

        // Aktualizace UI
        document.querySelectorAll('[id^="mode-"]').forEach(btn => btn.classList.remove('active'));
        document.getElementById(`mode-${mode}`).classList.add('active');

        console.log(`🎮 Režim změněn na: ${mode}`);
    }

    togglePhysics() {
        this.isRunning = !this.isRunning;
        const btn = document.getElementById('physics-toggle');
        btn.textContent = this.isRunning ? '⏸️ Pauza' : '▶️ Pokračovat';
        console.log(this.isRunning ? 'Simulace obnovena' : 'Simulace pozastavena');
    }

    clearUniverse() {
        this.objects.forEach(obj => {
            this.scene.remove(obj.mesh);
            if (obj.glow) this.scene.remove(obj.glow);
        });

        this.explosions.forEach(particle => {
            this.scene.remove(particle.mesh);
        });

        this.objects = [];
        this.explosions = [];
        this.simulationTime = 0;

        console.log('💥 Vesmír vymazán!');
    }

    resetCamera() {
        this.camera.position.set(0, 50, 100);
        this.camera.lookAt(0, 0, 0);
        this.controls.reset();
        console.log('📷 Kamera resetována');
    }

    toggleTrails() {
        this.enableTrails = !this.enableTrails;
        console.log(`✨ Stopy: ${this.enableTrails ? 'zapnuto' : 'vypnuto'}`);
    }

    toggleCollisions() {
        this.enableCollisions = !this.enableCollisions;
        console.log(`💥 Kolize: ${this.enableCollisions ? 'zapnuto' : 'vypnuto'}`);
    }

    toggleRelativity() {
        this.enableRelativity = !this.enableRelativity;
        console.log(`⚡ Relativita: ${this.enableRelativity ? 'zapnuto' : 'vypnuto'}`);
    }
}

// Globální instance
let universe;

// Globální funkce pro UI
function setMode(mode) {
    if (universe) universe.setMode(mode);
}

function updateMass(value) {
    if (universe) {
        universe.selectedMass = Math.pow(10, parseFloat(value));
        document.getElementById('mass-value').textContent = universe.selectedMass.toExponential(1) + ' kg';
    }
}

function updateVelocity(value) {
    if (universe) {
        universe.selectedVelocity = parseFloat(value);
        document.getElementById('velocity-value').textContent = value + ' km/s';
    }
}

function updateSize(value) {
    if (universe) {
        universe.selectedSize = parseFloat(value);
        document.getElementById('size-value').textContent = value + 'x';
    }
}

function updateTimeScale(value) {
    if (universe) {
        universe.timeScale = parseFloat(value);
        document.getElementById('time-scale-value').textContent = value + 'x';
    }
}

function updateGravity(value) {
    if (universe) {
        universe.gravityMultiplier = parseFloat(value) / 100;
        document.getElementById('gravity-value').textContent = value + '%';
    }
}

function togglePhysics() { if (universe) universe.togglePhysics(); }
function createSolarSystem() { if (universe) universe.createSolarSystem(); }
function createBinarySystem() { if (universe) universe.createBinarySystem(); }
function createGalaxy() { if (universe) universe.createGalaxy(); }
function createAsteroidField() { if (universe) universe.createAsteroidField(); }
function toggleTrails() { if (universe) universe.toggleTrails(); }
function toggleCollisions() { if (universe) universe.toggleCollisions(); }
function toggleRelativity() { if (universe) universe.toggleRelativity(); }
function clearUniverse() { if (universe) universe.clearUniverse(); }

// Inicializace při načtení stránky
document.addEventListener('DOMContentLoaded', async () => {
    try {
        // Kontrola WebGL podpory
        if (!window.WebGLRenderingContext) {
            throw new Error('WebGL není podporováno');
        }

        // Kontrola Three.js
        if (!window.THREE) {
            throw new Error('Three.js se nenačetl');
        }

        // Kontrola OrbitControls
        if (!THREE.OrbitControls) {
            throw new Error('OrbitControls se nenačetly');
        }

        // Inicializace vesmíru
        universe = new Universe3D();

        console.log(`
🌌 3D UNIVERSE SIMULATOR - Instrukce:

🎮 OVLÁDÁNÍ:
• Levé tlačítko myši - Vytvoření objektu
• Pravé tlačítko myši - Rotace kamery
• Kolečko myši - Zoom
• Střední tlačítko - Pan
• Mezerník - Pauza/Pokračování
• R - Reset kamery
• 1-5 - Rychlý výběr objektu

🔬 FYZIKA:
• Realistická 3D gravitace
• Kolize a fúze objektů
• Hvězdná evoluce
• Particle systémy
• Real-time statistiky

🌟 SCÉNÁŘE:
• Sluneční soustava
• Dvojhvězda
• Galaxie
• Asteroidové pole

Užijte si brutální 3D vesmírnou simulaci! 🚀
        `);

    } catch (error) {
        console.error('Chyba při inicializaci:', error);
        document.getElementById('loading').textContent = '❌ Chyba: ' + error.message;
    }
});