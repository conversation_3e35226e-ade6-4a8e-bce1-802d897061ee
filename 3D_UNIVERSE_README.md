# 🌌 3D Universe Simulator - Brutální Vesmírná Simulace

Kompletní **3D vesmírná simulace** s realistickou fyzikou, gravi<PERSON><PERSON><PERSON>, hvězdnou evolucí a spektakulárními vizuálními efekty!

## 🚀 **Spuštění simulace**

### Lokální spuštění:
```bash
python -m http.server 8001
```
Pak otevřete: `http://localhost:8001/universe-3d.html`

## 🎮 **Ovládání**

### 🖱️ **Myš:**
- **Levé tla<PERSON>ko** - Vytvoření objektu na pozici kurzoru
- **Pravé tla<PERSON>ko + tažení** - Rotace kamery kolem středu
- **<PERSON><PERSON><PERSON>ko** - Zoom in/out
- **Střední tla<PERSON> + tažení** - <PERSON> (posun kamery)
- **Hover** - Zobrazení informací o objektu

### ⌨️ **Klávesnice:**
- **Mezerník** - <PERSON>uza/Pokračování simulace
- **R** - Reset kamery na výchozí pozici
- **1-5** - Rychlý výběr typu objektu
- **Delete** - Vymazání celého vesmíru

### 📱 **Touch (mobily):**
- **Tap** - Vytvoření objektu
- **Drag** - Rotace kamery
- **Pinch** - Zoom

## 🔬 **3D Objekty**

### **🪐 Planety**
- **3D sféry** s procedurálními texturami
- **Kontinenty a oceány** generované v real-time
- **Různé barvy** podle složení
- **Realistické velikosti** podle hmotnosti

### **⭐ Hvězdy**
- **Glow efekty** simulující svítivost
- **Barvy podle teploty** (modrá → červená)
- **Dynamické změny** během evoluce
- **Volumetrické osvětlení**

### **🕳️ Černé díry**
- **Event horizon** (černá sféra)
- **Accretion disk** (oranžový prstenec)
- **Gravitační efekty** na okolní objekty
- **Hawkingovo vyzařování**

### **☄️ Asteroidy**
- **Nepravidelné tvary** (dodecahedron)
- **Hrubé povrchy** s metalickými vlastnostmi
- **Chaotické rotace**
- **Různé velikosti**

### **🚀 Vesmírné lodě**
- **Kónické tvary** s emisivními materiály
- **Zelené barvy** pro rozlišení
- **Umělé objekty** s nízkou hmotností

## ⚗️ **3D Fyzikální engine**

### **🌍 Gravitace**
```
F = G × m₁ × m₂ / r²
```
- **N-body simulace** ve 3D prostoru
- **Verlet integrace** pro stabilitu
- **Realistické síly** mezi všemi objekty
- **Optimalizace výkonu** pro stovky objektů

### **💥 Kolize a fúze**
- **3D detekce kolizí** podle poloměrů
- **Zachování hybnosti** ve 3D
- **Dynamické změny velikosti** objektů
- **Particle systémy** pro exploze

### **⭐ Hvězdná evoluce**
- **Jaderná fúze** s úbytkem hmotnosti
- **Změny barvy** podle teploty
- **Supernovy** s masivními explozemi
- **Transformace** na černé díry

### **🎆 Particle systémy**
- **3D exploze** s až 100 částicemi
- **Realistická fyzika** částic
- **Fade-out efekty** v čase
- **Různé barvy** podle typu exploze

## 🌟 **Předpřipravené 3D scénáře**

### **☀️ Sluneční soustava**
- **Slunce** ve středu s glow efektem
- **8 planet** na realistických orbitách
- **3D rozložení** s malými variacemi v Y ose
- **Správné orbitální rychlosti**

### **⭐⭐ Dvojhvězda**
- **Dva sluneční objekty** obíhající kolem společného těžiště
- **5 planet** na vzdálených orbitách
- **Komplexní gravitační interakce**
- **Spektakulární vizuální efekty**

### **🌌 Galaxie**
- **Centrální černá díra** (Sagittarius A*)
- **120+ hvězd** ve 4 spirálních ramenech
- **3D spirální struktura**
- **Mix hvězd a černých děr**

### **☄️ Asteroidové pole**
- **Centrální hvězda** pro gravitaci
- **100 asteroidů** v pásu
- **Chaotické orbity** s variacemi
- **Různé velikosti** a rychlosti

## 📊 **3D Vizualizace**

### **🎨 Materiály a textury:**
- **PBR materiály** (Physically Based Rendering)
- **Procedurální textury** pro planety
- **Emisivní materiály** pro hvězdy
- **Metalické povrchy** pro asteroidy
- **Transparentní efekty** pro glow

### **💡 Osvětlení:**
- **Ambientní světlo** pro základní viditelnost
- **Směrové světlo** simulující vzdálené hvězdy
- **Dynamické stíny** s PCF soft shadows
- **Tone mapping** pro realistické barvy

### **🌟 Speciální efekty:**
- **10,000 hvězd** na pozadí
- **Volumetrické glow** kolem hvězd
- **Particle trails** pro stopy objektů
- **Fog efekt** pro atmosféru
- **Real-time rotace** všech objektů

## 📈 **Výkon a optimalizace**

### **🚀 3D Rendering:**
- **WebGL** s Three.js
- **Frustum culling** pro objekty mimo obrazovku
- **LOD systém** pro vzdálené objekty
- **Batch rendering** pro particle systémy

### **⚡ Fyzikální optimalizace:**
- **Spatial partitioning** pro N-body problém
- **Adaptive timestep** podle výkonu
- **Collision detection** pouze pro blízké objekty
- **Memory pooling** pro particle systémy

### **📱 Mobilní podpora:**
- **Responsivní UI** pro touch zařízení
- **Optimalizované shadery** pro slabší GPU
- **Adaptive quality** podle výkonu
- **Touch gestures** pro navigaci

## 🎯 **Pokročilé 3D funkce**

### **📷 Kamera systém:**
- **Orbit controls** pro plynulou navigaci
- **Smooth damping** pro realistický pohyb
- **Auto-focus** na vybrané objekty
- **Zoom limits** pro optimální zobrazení

### **🎮 Interakce:**
- **3D raycasting** pro přesné klikání
- **Hover efekty** s informačními panely
- **Real-time tooltips** s fyzikálními daty
- **Visual feedback** pro všechny akce

### **📊 Real-time statistiky:**
- **FPS counter** pro monitoring výkonu
- **Počet objektů** v simulaci
- **Celková hmotnost** systému
- **Celková energie** (kinetická)
- **Simulační čas** s formátováním

## 🔬 **Vědecká přesnost**

### **✅ Fyzikálně správné:**
- **3D Newtonova gravitace** pro všechny objekty
- **Zachování hybnosti** ve všech směrech
- **Realistické poloměry** podle hmotnosti
- **Správné barvy hvězd** podle teploty
- **Orbitální mechanika** podle Keplerových zákonů

### **🎨 Vizuální vylepšení:**
- **Škálované velikosti** pro lepší viditelnost
- **Zrychlený čas** pro pozorovatelné změny
- **Zjednodušené textury** pro výkon
- **Stylizované efekty** pro dramatičnost

## 🎓 **Vzdělávací hodnota**

### **3D Koncepty:**
- **Prostorová geometrie** a vektory
- **3D transformace** a rotace
- **Perspektivní projekce**
- **Osvětlení a stínování**

### **Fyzikální principy:**
- **Gravitace ve 3D prostoru**
- **Orbitální mechanika**
- **Zachování veličin**
- **Hvězdná evoluce**

## 🎮 **Herní výzvy**

### **🏆 3D Achievementy:**
1. **Orbital Master** - Vytvoř stabilní orbitu trvající 100+ cyklů
2. **Galaxy Builder** - Postav galaxii s 50+ objekty
3. **Supernova Trigger** - Způsob hvězdnou explozi
4. **Black Hole Feeder** - Nech černou díru pohltit 10 objektů
5. **Asteroid Wrangler** - Vytvoř asteroidové pole se 100+ objekty

### **🎯 Experimenty:**
- **Gravitační prak** - Použij planetu k urychlení asteroidu
- **Trojitý systém** - Vytvoř stabilní systém 3 hvězd
- **Galaktická kolize** - Nech se srazit dvě galaxie
- **Planetární bombardování** - Nasměruj asteroidy na planetu

---

## 🚀 **Začněte experimentovat!**

1. **Otevřete simulaci** v prohlížeči
2. **Klikněte "☀️ Sluneční soustava"** pro začátek
3. **Rotujte kameru** pravým tlačítkem myši
4. **Zoomujte** kolečkem pro detail
5. **Klikněte kamkoliv** pro vytvoření nové planety
6. **Sledujte** jak gravitace ovlivňuje orbity!

**Užijte si brutální 3D vesmírnou simulaci s realistickou fyzikou! 🌌**

*Simulace využívá WebGL a Three.js pro maximální výkon a vizuální kvalitu.*
