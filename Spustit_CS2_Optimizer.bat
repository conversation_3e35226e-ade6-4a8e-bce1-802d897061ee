@echo off
echo ========================================
echo    CS2 Network Optimizer
echo    Spoustim jako administrator...
echo ========================================
echo.

REM Kontrola admin prav
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Spusteno jako administrator
    echo.
) else (
    echo [!] Potreba administratorska prava!
    echo Kliknete pravym tlacitkem a "Spustit jako spravce"
    echo.
    pause
    exit /b 1
)

REM Kontrola Python
python --version >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Python nalezen
    echo.
) else (
    echo [!] Python neni nainstalovany!
    echo Stahni z: https://python.org
    echo.
    pause
    exit /b 1
)

REM Spusteni aplikace
echo Spoustim CS2 Network Optimizer...
echo.
python CS2_Network_Optimizer.py

echo.
echo Aplikace ukoncena.
pause
