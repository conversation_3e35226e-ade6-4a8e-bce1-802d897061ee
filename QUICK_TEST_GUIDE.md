# 🧠 MindFlow - Quick Test Guide

**Rychlý návod pro testování MindFlow aplikace během 5 minut**

## 🚀 **<PERSON><PERSON><PERSON><PERSON> spuštění**

### **1. <PERSON><PERSON><PERSON> nastavení:**
```bash
chmod +x test-setup.sh
./test-setup.sh
```

### **2. Spuštění demo:**
```bash
./demo.sh
```

## 📱 **Co testovat (5 minut)**

### **⏱️ Minuta 1: Základn<PERSON> funkce**
1. **Spusť aplikaci** - <PERSON><PERSON><PERSON> by se nač<PERSON>t bez chyb
2. **Zkontroluj UI** - Moderní design s gradientem
3. **Test navigace** - Přepni mezi 5 taby (Home, Meditation, Progress, Community, Profile)
4. **Ov<PERSON><PERSON> responsivnost** - Smooth animace a přechody

### **⏱️ Minuta 2: Mood Tracking**
1. **Klikni na nálady** - Vyzkoušej všech 5 emotikonů (🤩😊😐😔😢)
2. **Zkontroluj feedback** - <PERSON><PERSON><PERSON> by se zobrazit zpráva
3. **<PERSON><PERSON><PERSON><PERSON> persistence** - Vybraná nálada zůstane označená
4. **Sleduj analytics** - V konzoli uvidíš event tracking

### **⏱️ Minuta 3: Reklamy a monetizace**
1. **Najdi banner reklamu** - Měla by být viditelná na Home screen
2. **Počkej na rotaci** - Reklama se změní za 5 sekund
3. **Klikni na reklamu** - Bezpečné (test ads), sleduj revenue tracking
4. **Zkontroluj různé typy** - Spotify, Headspace, Audible, atd.

### **⏱️ Minuta 4: Pokročilé funkce**
1. **Quick Meditation** - Klikni na červené tlačítko
2. **Progress Stats** - Zkontroluj čísla (meditace, streak, nálada)
3. **Premium features** - Zkus přístup k premium obsahu
4. **Notifications** - Ověř, že se zobrazují připomínky

### **⏱️ Minuta 5: Performance a stabilita**
1. **Rychlé přepínání** - Rychle přepínej mezi taby
2. **Memory test** - Nech aplikaci běžet, sleduj performance
3. **Restart test** - Zavři a znovu otevři aplikaci
4. **Data persistence** - Ověř, že data zůstala uložená

## ✅ **Rychlý checklist**

```
□ Aplikace se spustí bez crashů
□ UI vypadá moderně a profesionálně  
□ Navigace funguje smooth
□ Mood tracking ukládá data
□ Reklamy se zobrazují a rotují
□ Analytics trackují eventy (konzole)
□ Quick meditation funguje
□ Stats se aktualizují
□ Premium prompt se zobrazuje
□ Data přežijí restart aplikace
```

## 🎯 **Co sledovat v konzoli**

```javascript
// Analytics events
📊 Event tracked: mood_selected {mood: "good"}
📊 Event tracked: screen_view {screen_name: "Meditation"}
📊 Event tracked: ad_impression {ad_id: "spotify_premium"}

// Ad revenue tracking  
💰 Ad impression: spotify_premium, Revenue: $0.0125
💰 Ad click: headspace_trial, Revenue: $1.20

// Performance metrics
📊 FPS: 60, Objects: 0, Time: 45.2s
```

## 🐛 **Časté problémy a řešení**

### **Aplikace se nespustí:**
```bash
# Vyčisti cache
npm start -- --reset-cache

# Reinstaluj dependencies
rm -rf node_modules && npm install

# Android specific
cd android && ./gradlew clean && cd ..
```

### **Reklamy se nezobrazují:**
- Zkontroluj internet připojení
- Ověř, že používáš test ad units
- Restartuj aplikaciju

### **Analytics nefungují:**
- Otevři Developer Tools / Console
- Zkontroluj, že TEST_MODE je enabled
- Ověř, že events se logují

### **Performance problémy:**
- Zavři ostatní aplikace
- Zkontroluj dostupnou RAM
- Použij release build pro lepší performance

## 📊 **Test metriky**

### **Úspěšný test:**
- **Startup time:** < 3 sekundy
- **FPS:** > 30 (ideálně 60)
- **Memory usage:** < 200MB
- **Crash rate:** 0%
- **Ad load time:** < 2 sekundy
- **Navigation lag:** < 100ms

### **Revenue tracking test:**
```
Ad Impressions: 5+ za minutu
Ad Clicks: 1-2 za test session  
Revenue per session: $0.05-0.20
Analytics events: 10+ za minutu
```

## 🎮 **Demo scénáře**

### **Scénář 1: Nový uživatel**
1. Otevři aplikaci poprvé
2. Projdi onboarding (pokud existuje)
3. Nastav první náladu
4. Vyzkoušej quick meditation
5. Zkontroluj progress

### **Scénář 2: Returning user**
1. Otevři aplikaci s existujícími daty
2. Aktualizuj dnešní náladu
3. Pokračuj v streak
4. Zkontroluj týdenní statistiky
5. Vyzkoušej premium funkci

### **Scénář 3: Premium upgrade**
1. Jako free user zkus premium funkci
2. Zobrazí se upgrade prompt
3. Projdi subscription flow
4. Ověř premium přístup
5. Test premium features

## 💡 **Pro tips**

### **Rychlé testování:**
- Použij **Fast Mode** - všechny timery jsou zrychlené
- **Mock data** jsou přednastavená
- **Test ads** jsou bezpečné na klikání
- **Console logging** ukazuje všechny eventy

### **Debugging:**
```bash
# Zobraz device logs
npx react-native log-android  # Android
npx react-native log-ios      # iOS

# Network debugging
# Otevři Chrome DevTools pro network monitoring
```

### **Performance monitoring:**
- Sleduj FPS counter v aplikaci
- Monitoruj memory usage
- Zkontroluj battery drain
- Test na různých zařízeních

## 🎉 **Úspěšný test znamená:**

1. **✅ Funkčnost** - Všechny features fungují
2. **✅ Stabilita** - Žádné crashe nebo freezy
3. **✅ Performance** - Smooth 60 FPS
4. **✅ Monetizace** - Reklamy se zobrazují a trackují
5. **✅ Analytics** - Eventy se správně logují
6. **✅ UX** - Intuitivní a příjemné používání
7. **✅ Data** - Správné ukládání a načítání

---

## 🚀 **Ready to test?**

```bash
./demo.sh
```

**Užij si testování MindFlow - aplikace, která změní životy milionů lidí! 🧠💙**

*"Your mind is your most powerful tool. Let's make it your best friend." - MindFlow*
