@echo off
echo MindFlow Demo Launcher
echo ========================
echo.
echo Demo Features:
echo - Mood tracking with 5 different moods
echo - Guided meditation sessions  
echo - Smart ad integration (test ads)
echo - Progress analytics
echo - Premium features preview
echo - Push notifications
echo.
echo Starting React Native Metro bundler...
echo.

start "Metro Bundler" cmd /k "npm start"

echo Waiting 10 seconds for Metro to start...
timeout /t 10 /nobreak >nul

echo.
echo Now starting Android app...
echo (Make sure you have Android emulator running)
echo.

npm run android

echo.
echo Demo completed!
pause
